.add-container-quotation {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .fixed-title-box {
    position: fixed;
    top: 75px;
    left: 0;
    right: 0;
    z-index: 99;
    background: #fff;
    padding: 10px 10px 10px 0;
    margin: 0 15px;
    height: 46px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: center;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 22px;
        background: linear-gradient(to bottom, #1797e1, #40a9ff);
        border-radius: 2px;
      }
    }

    .go_back {
      display: flex;
      align-items: center;
      color: #1797e1;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(23, 151, 225, 0.1);
        border-color: rgba(23, 151, 225, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(23, 151, 225, 0.15);
      }

      .anticon {
        margin-right: 8px;
      }
    }
  }

  .content-container {
    margin-top: 43px;
    overflow-y: auto;
    background-color: #f9fafc;
    scrollbar-width: thin;
    scrollbar-color: #eaeaea transparent;
    scrollbar-gutter: stable;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #eaeaea;
      border-radius: 4px;
      border: 2px solid transparent;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #d0d0d0;
    }

    .form-container {
      background-color: #fff;
      overflow: hidden;

      > .ant-card-body {
        height: calc(100vh - 190px);
        overflow-y: auto;
        padding: 12px 16px;
        background-color: #fff;
      }
    }

    .form-section {
      border-radius: 8px;
      background-color: white;
      border: 1px solid #eaedf0;
      transition: all 0.3s ease;
      position: relative;
      margin-bottom: 8px;

      .ant-card-body {
        background: #fff;
        padding: 12px 16px;
      }

      .ant-card-head {
        border-bottom: 1px solid rgba(23, 151, 225, 0.1);
        background: linear-gradient(
          to right,
          rgba(23, 151, 225, 0.05),
          rgba(64, 169, 255, 0.02)
        );
        min-height: 40px;
        padding: 0 16px;

        .ant-card-head-title {
          font-size: 15px;
          font-weight: 600;
          color: #1f2937;
          position: relative;
          padding-left: 10px;

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background: linear-gradient(to bottom, #1797e1, #40a9ff);
            border-radius: 2px;
          }
        }
      }
    }

    .ant-row {
      margin-bottom: 8px;
    }

    .ant-form-item {
      margin-bottom: 6px;

      .ant-form-item-label {
        padding-bottom: 2px;

        > label {
          color: #374151;
          font-weight: 500;
          font-size: 13px;
        }
      }

      .ant-form-item-explain {
        font-size: 12px;
        min-height: 16px;
      }

      .ant-input,
      .ant-input-number,
      .ant-select .ant-select-selector,
      .ant-picker {
        border-radius: 4px;
        border-color: #e5e7eb;
        transition: all 0.3s;
        height: 32px;
        padding: 0 8px;
        font-size: 13px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.01);
        display: flex;
        align-items: center;

        &:hover {
          border-color: #1797e1;
          box-shadow: 0 0 0 1px rgba(23, 151, 225, 0.1);
        }

        &:focus {
          border-color: #1797e1;
          box-shadow: 0 0 0 2px rgba(23, 151, 225, 0.1);
          outline: none;
        }
      }

      .ant-input {
        line-height: 32px;
      }

      .ant-select-multiple {
        .ant-select-selector {
          height: auto !important;
          min-height: 32px !important;
          padding: 2px 5px !important;
        }

        .ant-select-selection-overflow {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
        }

        .ant-select-selection-item {
          height: 22px;
          line-height: 20px !important;
          margin-top: 2px;
          margin-bottom: 2px;
        }

        .ant-select-selection-search {
          margin-top: 2px;
          margin-bottom: 2px;
        }
      }

      .ant-input-number {
        width: 100%;

        .ant-input-number-handler-wrap {
          border-radius: 0 4px 4px 0;
        }

        .ant-input-number-input-wrap {
          width: 100%;
        }

        .ant-input-number-input {
          height: 30px;
          line-height: 30px;
          padding: 0 8px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-top: 0;
          margin-bottom: 0;
        }

        input.ant-input-number-input {
          line-height: 1;
          padding-top: 0;
          padding-bottom: 0;
        }
      }

      .ant-switch {
        background-color: #d1d5db;
        height: 18px;
        min-width: 36px;

        .ant-switch-handle {
          width: 14px;
          height: 14px;
          top: 2px;
          left: 2px;
          transition: all 0.3s;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        &.ant-switch-checked {
          background: linear-gradient(90deg, #1797e1, #40a9ff);

          .ant-switch-handle {
            left: calc(100% - 18px);
          }
        }

        &:hover:not(.ant-switch-disabled) {
          background-color: #b0b7c3;

          &.ant-switch-checked {
            background: linear-gradient(90deg, #40a9ff, #1797e1);
          }
        }
      }
    }

    .full-width {
      width: 100%;
    }

    .fixed-bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      height: 56px;
      border-top: 1px solid #e6e8eb;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      background-color: #fff;
      padding: 0 24px;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
      z-index: 100;
      backdrop-filter: blur(5px);
      background-color: rgba(255, 255, 255, 0.95);

      .ant-btn {
        height: 32px;
        border-radius: 4px;
        padding: 0 16px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s;
        border: 1px solid #e6e8eb;

        &:not(:last-child) {
          margin-right: 12px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        }

        &.ant-btn-primary {
          background: linear-gradient(90deg, #1797e1, #40a9ff);
          border: none;
          box-shadow: 0 1px 4px rgba(23, 151, 225, 0.2);
          color: white;

          &:hover {
            background: linear-gradient(90deg, #40a9ff, #1797e1);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(23, 151, 225, 0.25);
          }
        }
      }
    }
  }
}

.email-extract-modal {
  .ant-modal {
    max-height: 95vh;
    margin: 0 auto;
    top: 2.5vh;
  }

  .ant-modal-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 1);
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .ant-modal-close-x {
      font-size: 14px;
      color: #64748b;
      line-height: 30px;
    }
  }

  .ant-modal-content {
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border: none;
    height: 90vh;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 16px;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.5),
        transparent
      );
    }
  }

  .ant-modal-header {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.03) 0%,
      rgba(147, 197, 253, 0.05) 50%,
      rgba(219, 234, 254, 0.03) 100%
    );
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    flex-shrink: 0;
    padding: 16px;
    position: relative;
    margin-bottom: 0px;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 32px;
      right: 32px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.2),
        transparent
      );
    }

    .ant-modal-title {
      font-size: 20px;
      font-weight: 700;
      background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      display: flex;
      align-items: center;
      gap: 12px;
      letter-spacing: -0.025em;

      &::before {
        content: "";
        width: 4px;
        height: 24px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border-radius: 2px;
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
      }
    }
  }

  .ant-modal-body {
    padding: 0;
    background: transparent;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .ant-modal-footer {
    padding: 16px;
    background: linear-gradient(
      135deg,
      rgba(248, 250, 252, 0.8) 0%,
      rgba(241, 245, 249, 0.9) 100%
    );
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    backdrop-filter: blur(8px);
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 32px;
      right: 32px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.2),
        transparent
      );
    }

    .ant-btn {
      margin-left: 0;
      height: 40px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 14px;
      padding: 0 24px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &.ant-btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        color: #ffffff;
        box-shadow:
          0 4px 14px rgba(59, 130, 246, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          transition: left 0.5s;
        }

        &:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
          transform: translateY(-2px);
          box-shadow:
            0 8px 25px rgba(59, 130, 246, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
          color: #94a3b8;
          transform: none;
          box-shadow: none;

          &::before {
            display: none;
          }
        }
      }

      &.ant-btn-default {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(59, 130, 246, 0.2);
        color: #475569;
        backdrop-filter: blur(8px);

        &:hover {
          background: rgba(59, 130, 246, 0.05);
          border-color: rgba(59, 130, 246, 0.4);
          color: #3b82f6;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        &:disabled {
          background: rgba(241, 245, 249, 0.5);
          border-color: rgba(203, 213, 225, 0.5);
          color: #94a3b8;
          transform: none;
          box-shadow: none;
        }
      }
    }
  }

  .modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
    padding: 16px 16px 0;
  }

  .steps-container {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 100%
    );
    padding: 16px;
    border-radius: 16px;
    backdrop-filter: blur(12px);
    border: 1px solid rgba(59, 131, 246, 0.342);
    flex-shrink: 0;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.02) 0%,
        rgba(147, 197, 253, 0.03) 50%,
        rgba(219, 234, 254, 0.02) 100%
      );
      pointer-events: none;
    }

    .ant-steps {
      position: relative;
      z-index: 1;

      .ant-steps-item {
        .ant-steps-item-icon {
          width: 40px;
          height: 40px;
          line-height: 38px;
          font-size: 18px;
          border-width: 2px;
          border-radius: 50%;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(
              135deg,
              transparent 0%,
              rgba(255, 255, 255, 0.3) 100%
            );
            opacity: 0;
            transition: opacity 0.3s ease;
          }
        }

        .ant-steps-item-title {
          font-size: 15px;
          font-weight: 600;
          margin-top: 12px;
          letter-spacing: -0.025em;
          transition: all 0.3s ease;
        }

        .ant-steps-item-description {
          font-size: 13px;
          color: #64748b;
          margin-top: 6px;
          line-height: 1.4;
        }

        &.ant-steps-item-wait {
          .ant-steps-item-icon {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-color: #cbd5e1;
            color: #94a3b8;
            box-shadow: 0 2px 8px rgba(148, 163, 184, 0.1);
          }

          .ant-steps-item-title {
            color: #94a3b8;
          }
        }

        &.ant-steps-item-process {
          .ant-steps-item-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #3b82f6;
            color: #ffffff;
            box-shadow:
              0 4px 14px rgba(59, 130, 246, 0.3),
              0 0 0 4px rgba(59, 130, 246, 0.1);
            animation: pulse-glow 2s infinite;

            &::before {
              opacity: 1;
            }
          }

          .ant-steps-item-title {
            color: #3b82f6;
            font-weight: 700;
          }
        }

        &.ant-steps-item-finish {
          .ant-steps-item-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-color: #10b981;
            color: #ffffff;
            box-shadow:
              0 4px 14px rgba(16, 185, 129, 0.3),
              0 0 0 4px rgba(16, 185, 129, 0.1);

            &::before {
              opacity: 1;
            }
          }

          .ant-steps-item-title {
            color: #10b981;
            font-weight: 600;
          }

          .ant-steps-item-tail::after {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            height: 2px;
          }
        }

        .ant-steps-item-tail::after {
          background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 100%);
          height: 2px;
          border-radius: 1px;
        }
      }
    }
  }

  @keyframes pulse-glow {
    0%,
    100% {
      box-shadow:
        0 4px 14px rgba(59, 130, 246, 0.3),
        0 0 0 4px rgba(59, 130, 246, 0.1);
    }
    50% {
      box-shadow:
        0 6px 20px rgba(59, 130, 246, 0.4),
        0 0 0 6px rgba(59, 130, 246, 0.15);
    }
  }

  .step-content-container {
    flex: 1;
    overflow: hidden;
    min-height: 0;
    height: calc(100% - 70px);
  }

  .step-card {
    background: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(248, 250, 252, 0.9) 100%
    );
    border-radius: 20px;
    backdrop-filter: blur(12px);
    border: 1px solid rgba(59, 131, 246, 0.39);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    height: 100%;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.3),
        transparent
      );
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.01) 0%,
        rgba(147, 197, 253, 0.02) 50%,
        rgba(219, 234, 254, 0.01) 100%
      );
      pointer-events: none;
    }

    .ant-card-head {
      background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.03) 0%,
        rgba(147, 197, 253, 0.05) 50%,
        rgba(219, 234, 254, 0.03) 100%
      );
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      padding: 16px;
      flex-shrink: 0;
      position: relative;
      z-index: 1;

      .ant-card-head-title {
        font-size: 18px;
        font-weight: 700;
        background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        display: flex;
        align-items: center;
        gap: 12px;
        letter-spacing: -0.025em;

        &::before {
          content: "";
          width: 4px;
          height: 20px;
          background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
          border-radius: 2px;
          box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
        }
      }
    }

    .ant-card-body {
      padding: 16px;
      position: relative;
      z-index: 1;
      overflow-x: hidden;
      height: calc(100% - 60px);

      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(241, 245, 249, 0.5);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        }
      }
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .ant-progress {
    .ant-progress-outer {
      .ant-progress-inner {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);

        .ant-progress-bg {
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 12px;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent,
              rgba(255, 255, 255, 0.3),
              transparent
            );
            animation: shimmer 2s infinite;
          }
        }
      }
    }

    .ant-progress-text {
      font-weight: 600;
      color: #3b82f6;
      font-size: 14px;
    }
  }

  @keyframes shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  .ant-form {
    overflow-x: hidden;

    .ant-form-item-label > label {
      font-weight: 600;
      color: #374151;
      font-size: 14px;
      letter-spacing: -0.025em;
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-radius: 12px;
      border: 1px solid rgba(59, 130, 246, 0.2);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      height: 40px !important;
      min-height: 40px !important;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(8px);
      font-size: 14px;

      &:hover {
        border-color: rgba(59, 130, 246, 0.4);
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
      }

      &:focus,
      &.ant-input-focused,
      &.ant-select-focused .ant-select-selector {
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow:
          0 0 0 3px rgba(59, 130, 246, 0.1),
          0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-1px);
      }
    }

    .ant-input-number {
      width: 100% !important;

      .ant-input-number-input {
        height: 38px !important;
        border: none;
        background: transparent;
      }
      .ant-input-number-handler-wrap {
        border-start-end-radius: 12px;
        border-end-end-radius: 12px;
      }
    }

    .full-width {
      width: 100% !important;
    }

    .ant-input[disabled] {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      color: #64748b;
      border-color: rgba(203, 213, 225, 0.5);
    }

    .ant-select-multiple .ant-select-selector {
      height: auto !important;
      min-height: 40px !important;
      padding: 4px 8px !important;
    }

    .ant-select-single {
      height: 40px !important;
      min-height: 40px !important;
    }

    .ant-switch {
      background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
      border: 1px solid rgba(59, 130, 246, 0.2);

      &.ant-switch-checked {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
      }
    }
  }

  .ant-input {
    &[rows] {
      resize: vertical;
      min-height: 140px;
      max-height: 320px;
      font-family:
        "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Consolas", monospace;
      line-height: 1.6;
      border-radius: 16px;
      padding: 16px;
      background: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(248, 250, 252, 0.8) 100%
      );
      backdrop-filter: blur(8px);
      border: 1px solid rgba(59, 130, 246, 0.2);
      font-size: 14px;

      &:focus {
        background: #ffffff;
        border-color: #3b82f6;
        box-shadow:
          0 0 0 3px rgba(59, 130, 246, 0.1),
          0 8px 25px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
      }

      &:hover {
        border-color: rgba(59, 130, 246, 0.4);
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .ant-btn {
    border-radius: 12px;
    font-weight: 600;
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      color: #ffffff;
      box-shadow:
        0 4px 14px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        transform: translateY(-2px);
        box-shadow:
          0 8px 25px rgba(59, 130, 246, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
      }
    }

    &.ant-btn-default {
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(59, 130, 246, 0.2);
      color: #475569;
      backdrop-filter: blur(8px);

      &:hover {
        background: rgba(59, 130, 246, 0.05);
        border-color: rgba(59, 130, 246, 0.4);
        color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
      }
    }
  }

  .ant-alert {
    border-radius: 16px;
    margin-bottom: 20px;
    padding: 16px 20px;
    border: 1px solid;
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        currentColor,
        transparent
      );
      opacity: 0.3;
    }

    &.ant-alert-info {
      background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.05) 0%,
        rgba(147, 197, 253, 0.08) 100%
      );
      border-color: rgba(59, 130, 246, 0.2);
      color: #1e40af;

      .ant-alert-icon {
        color: #3b82f6;
      }

      .ant-alert-message {
        color: #1e40af;
        font-weight: 600;
      }

      .ant-alert-description {
        color: #1e3a8a;
      }
    }

    &.ant-alert-success {
      background: linear-gradient(
        135deg,
        rgba(16, 185, 129, 0.05) 0%,
        rgba(110, 231, 183, 0.08) 100%
      );
      border-color: rgba(16, 185, 129, 0.2);
      color: #065f46;

      .ant-alert-icon {
        color: #10b981;
      }

      .ant-alert-message {
        color: #065f46;
        font-weight: 600;
      }

      .ant-alert-description {
        color: #064e3b;
      }
    }
  }

  .ant-typography {
    h4,
    h5 {
      background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 700;
      margin: 24px 0 16px 0;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(59, 130, 246, 0.1);
      font-size: 16px;
      letter-spacing: -0.025em;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        border-radius: 1px;
      }
    }
  }

  .ai-extract-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.02) 0%,
      rgba(147, 197, 253, 0.05) 50%,
      rgba(219, 234, 254, 0.02) 100%
    );
    backdrop-filter: blur(12px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 20px;
    animation: overlayFadeIn 0.3s ease;

    .ai-extract-content {
      text-align: center;
      padding: 48px 32px;
      background: linear-gradient(
        145deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 100%
      );
      border-radius: 24px;
      backdrop-filter: blur(16px);
      border: 1px solid rgba(59, 130, 246, 0.1);
      box-shadow:
        0 25px 50px -12px rgba(59, 130, 246, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      max-width: 480px;
      width: 90%;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(59, 130, 246, 0.5),
          transparent
        );
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(59, 130, 246, 0.01) 0%,
          rgba(147, 197, 253, 0.02) 50%,
          rgba(219, 234, 254, 0.01) 100%
        );
        pointer-events: none;
      }

      .anticon {
        animation: aiPulse 2s infinite ease-in-out;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
      }

      h4 {
        position: relative;
        z-index: 1;
        margin-bottom: 16px !important;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        position: relative;
        z-index: 1;
        color: #64748b;
        font-size: 14px;
        line-height: 1.6;
      }

      @keyframes aiPulse {
        0%,
        100% {
          transform: scale(1) rotate(0deg);
          filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.2));
        }
        25% {
          transform: scale(1.05) rotate(1deg);
          filter: drop-shadow(0 6px 12px rgba(59, 130, 246, 0.3));
        }
        50% {
          transform: scale(1.1) rotate(0deg);
          filter: drop-shadow(0 8px 16px rgba(59, 130, 246, 0.4));
        }
        75% {
          transform: scale(1.05) rotate(-1deg);
          filter: drop-shadow(0 6px 12px rgba(59, 130, 246, 0.3));
        }
      }
    }
  }

  @keyframes overlayFadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(12px);
    }
  }

  .create-quotation-content {
    position: relative;
    z-index: 1;

    .anticon {
      animation: successBounce 1s ease-in-out;
      filter: drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3));
    }

    h4 {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    p {
      color: #64748b;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  @keyframes successBounce {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0) scale(1);
    }
    40% {
      transform: translateY(-8px) scale(1.05);
    }
    60% {
      transform: translateY(-4px) scale(1.02);
    }
  }

  @keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  .ant-form-item-has-error {
    .ant-input,
    .ant-input-number,
    .ant-select-selector,
    .ant-picker {
      border-color: #ef4444 !important;
      background: linear-gradient(
        135deg,
        rgba(239, 68, 68, 0.02) 0%,
        rgba(252, 165, 165, 0.05) 100%
      ) !important;
      box-shadow:
        0 0 0 3px rgba(239, 68, 68, 0.1),
        0 4px 12px rgba(239, 68, 68, 0.15) !important;

      &:hover,
      &:focus {
        border-color: #dc2626 !important;
        box-shadow:
          0 0 0 3px rgba(220, 38, 38, 0.15),
          0 6px 16px rgba(239, 68, 68, 0.2) !important;
      }
    }

    .ant-form-item-explain-error {
      color: #dc2626;
      font-weight: 500;
      font-size: 13px;
    }
  }

  @media (max-width: 768px) {
    .ant-modal {
      margin: 12px;
      max-width: calc(100vw - 24px);
      max-height: calc(100vh - 24px);
      top: 12px;
    }

    .ant-modal-content {
      max-height: calc(100vh - 24px);
      border-radius: 16px;
    }

    .ant-modal-header {
      padding: 20px 24px 16px;

      .ant-modal-title {
        font-size: 18px;

        &::before {
          width: 3px;
          height: 20px;
        }
      }
    }

    .ant-modal-body {
      padding: 0;
    }

    .modal-content {
      padding: 16px 24px 0;
    }

    .ant-modal-footer {
      padding: 16px 24px 20px;

      .ant-btn {
        height: 36px;
        font-size: 13px;
        padding: 0 16px;
        border-radius: 10px;
      }
    }

    .steps-container {
      padding: 20px 24px;
      margin-bottom: 16px;
      border-radius: 12px;

      .ant-steps-item {
        .ant-steps-item-description {
          display: none;
        }

        .ant-steps-item-icon {
          width: 32px;
          height: 32px;
          line-height: 30px;
          font-size: 14px;
        }

        .ant-steps-item-title {
          font-size: 13px;
          margin-top: 8px;
        }
      }
    }

    .step-card {
      border-radius: 16px;

      .ant-card-head {
        padding: 16px 20px 12px;

        .ant-card-head-title {
          font-size: 16px;

          &::before {
            width: 3px;
            height: 16px;
          }
        }
      }

      .ant-card-body {
        padding: 20px;
      }
    }

    .ant-form {
      .ant-input,
      .ant-input-number,
      .ant-select-selector,
      .ant-picker {
        height: 36px !important;
        min-height: 36px !important;
        border-radius: 10px;
        font-size: 13px;
      }

      .ant-input-number {
        .ant-input-number-input {
          height: 34px !important;
        }
      }

      .ant-select-multiple .ant-select-selector {
        min-height: 36px !important;
        padding: 2px 6px !important;
      }

      .ant-form-item-label > label {
        font-size: 13px;
      }
    }

    .ant-input[rows] {
      min-height: 120px;
      max-height: 240px;
      border-radius: 12px;
      padding: 12px;
      font-size: 13px;
    }

    .ai-extract-overlay {
      border-radius: 16px;

      .ai-extract-content {
        padding: 32px 24px;
        max-width: 340px;
        border-radius: 20px;

        .anticon {
          font-size: 40px !important;
        }

        h4 {
          font-size: 16px !important;
          margin-bottom: 12px !important;
        }

        p {
          font-size: 13px;
        }
      }
    }

    .ant-progress {
      .ant-progress-outer {
        .ant-progress-inner {
          border-radius: 8px;

          .ant-progress-bg {
            border-radius: 8px;
          }
        }
      }

      .ant-progress-text {
        font-size: 13px;
      }
    }

    .ant-alert {
      border-radius: 12px;
      padding: 12px 16px;
      margin-bottom: 16px;

      .ant-alert-message {
        font-size: 13px;
      }

      .ant-alert-description {
        font-size: 12px;
      }
    }
  }
}
