import React, { useState, useEffect } from "react";
import { Card, Tabs } from "antd";
import {
  TeamOutlined,
  UserOutlined,
  PartitionOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import "./index.less";
import UserManagement from "./components/UserManagement";
import DepartmentManagement from "./components/DepartmentManagement";

import type { TabsProps } from "antd";
const OrganizationManage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("1");
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: (
        <span>
          <UserOutlined />
          {t("organizationManagement.tabs.userManagement")}
        </span>
      ),
      children: <UserManagement />,
    },
    {
      key: "2",
      label: (
        <span>
          <TeamOutlined />
          {t("organizationManagement.tabs.departmentList")}
        </span>
      ),
      children: <DepartmentManagement />,
    },
  ];

  return (
    <div className="organization-manage-container">
      <Card
        title={
          <div className="page-header">
            <PartitionOutlined className="header-icon" />
            <span>{t("organizationManagement.title")}</span>
          </div>
        }
        className="main-card"
      >
        <Tabs
          defaultActiveKey="1"
          items={items}
          onChange={setActiveTab}
          activeKey={activeTab}
          destroyInactiveTabPane
        />
      </Card>
    </div>
  );
};

export default OrganizationManage;
