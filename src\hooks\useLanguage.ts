import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setLanguage, Language } from '@/store/slices/languageSlice';

export const useLanguage = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useAppDispatch();
  const { currentLanguage } = useAppSelector((state) => state.language);

  const changeLanguage = (language: Language) => {
    dispatch(setLanguage(language));
  };

  const isZhCN = currentLanguage === 'zh-CN';
  const isEnUS = currentLanguage === 'en-US';

  return {
    t,
    i18n,
    currentLanguage,
    changeLanguage,
    isZhCN,
    isEnUS,
  };
};
