import React from "react";
import { Modal, Form, Input, Select, Row, Col, Switch } from "antd";
import { useTranslation } from "react-i18next";
import { Department, User } from "../types";

const { Option } = Select;

interface UserFormModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
  editingUser: User | null;
  departments: Department[];
  form: any;
}

const UserFormModal: React.FC<UserFormModalProps> = ({
  visible,
  onCancel,
  onOk,
  editingUser,
  departments,
  form,
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      title={
        editingUser
          ? t("organizationManagement.userManagement.userFormModal.title.edit")
          : t("organizationManagement.userManagement.userFormModal.title.add")
      }
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      width={700}
      destroyOnClose
      centered
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.email"
              )}
              name="email"
              rules={[
                {
                  type: "email",
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.emailInvalid"
                  ),
                },
                {
                  max: 100,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.emailMaxLength"
                  ),
                },
                {
                  required: true,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.emailRequired"
                  ),
                },
              ]}
            >
              <Input
                placeholder={t(
                  "organizationManagement.userManagement.userFormModal.placeholders.email"
                )}
              />
            </Form.Item>
          </Col>
          {!editingUser && (
            <Col span={12}>
              <Form.Item
                label={t(
                  "organizationManagement.userManagement.userFormModal.fields.password"
                )}
                name="password"
                rules={[
                  {
                    required: !editingUser,
                    message: t(
                      "organizationManagement.userManagement.userFormModal.validation.passwordRequired"
                    ),
                  },
                  {
                    max: 50,
                    message: t(
                      "organizationManagement.userManagement.userFormModal.validation.passwordMaxLength"
                    ),
                  },
                ]}
                tooltip={
                  editingUser
                    ? t(
                        "organizationManagement.userManagement.userFormModal.tooltips.passwordEdit"
                      )
                    : ""
                }
              >
                <Input.Password
                  placeholder={
                    editingUser
                      ? t(
                          "organizationManagement.userManagement.userFormModal.placeholders.passwordEdit"
                        )
                      : t(
                          "organizationManagement.userManagement.userFormModal.placeholders.password"
                        )
                  }
                />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.fullname"
              )}
              name="fullname"
              rules={[
                // { required: true, message: "请输入姓名" },
                {
                  max: 100,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.fullnameMaxLength"
                  ),
                },
              ]}
            >
              <Input
                placeholder={t(
                  "organizationManagement.userManagement.userFormModal.placeholders.fullname"
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.department"
              )}
              name="departmentid"
              rules={[
                {
                  required: true,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.departmentRequired"
                  ),
                },
              ]}
            >
              <Select
                placeholder={t(
                  "organizationManagement.userManagement.userFormModal.placeholders.department"
                )}
              >
                {departments.map((dept) => (
                  <Option key={dept.departmentid} value={dept.departmentid}>
                    {dept.departname}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.phone"
              )}
              name="telephone"
              rules={[
                {
                  max: 100,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.phoneMaxLength"
                  ),
                },
              ]}
            >
              <Input
                placeholder={t(
                  "organizationManagement.userManagement.userFormModal.placeholders.phone"
                )}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.userIdentity"
              )}
              name="useridentity"
              rules={[
                {
                  required: true,
                  message: t(
                    "organizationManagement.userManagement.userFormModal.validation.userIdentityRequired"
                  ),
                },
              ]}
              initialValue={1}
            >
              <Select
                placeholder={t(
                  "organizationManagement.userManagement.placeholders.selectUserIdentity"
                )}
                options={[
                  {
                    label: t(
                      "organizationManagement.userManagement.userIdentity.1"
                    ),
                    value: 1,
                  },
                  {
                    label: t(
                      "organizationManagement.userManagement.userIdentity.2"
                    ),
                    value: 2,
                  },
                  {
                    label: t(
                      "organizationManagement.userManagement.userIdentity.3"
                    ),
                    value: 3,
                  },
                  {
                    label: t(
                      "organizationManagement.userManagement.userIdentity.4"
                    ),
                    value: 4,
                  },
                  {
                    label: t(
                      "organizationManagement.userManagement.userIdentity.5"
                    ),
                    value: 5,
                  },
                ]}
              ></Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label={t(
                "organizationManagement.userManagement.userFormModal.fields.status"
              )}
              name="isvalid"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch
                checkedChildren={t(
                  "organizationManagement.userManagement.userFormModal.status.enabled"
                )}
                unCheckedChildren={t(
                  "organizationManagement.userManagement.userFormModal.status.disabled"
                )}
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          label={t(
            "organizationManagement.userManagement.userFormModal.fields.remarks"
          )}
          name="userremark"
          rules={[
            {
              max: 200,
              message: t(
                "organizationManagement.userManagement.userFormModal.validation.remarksMaxLength"
              ),
            },
          ]}
        >
          <Input.TextArea
            rows={3}
            placeholder={t(
              "organizationManagement.userManagement.userFormModal.placeholders.remarks"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserFormModal;
