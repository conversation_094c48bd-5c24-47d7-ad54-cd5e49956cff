import React, { useState, useEffect, useRef } from "react";
import { message, Modal } from "antd";
import type { TextAreaRef } from "antd/es/input/TextArea";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import "./index.less";
import ChatPanel from "./components/ChatPanel";
import QuotationList from "./components/QuotationList";
import QuotationDetailModal from "./components/QuotationDetailModal";
import NoAccessResult from "./components/NoAccessResult";
import {
  initializeAIAssistant,
  sendMessageToAI,
  chatCompletions,
} from "./services";
import { Message } from "./types";
import {
  generateSingleFieldCollectionRequest,
  formatFinalShippingInfo,
  generatePortConvertSystemPrompt,
  parsePortConvertResponse,
  ConvertedShippingData,
} from "./config/promptConfig";
import { getInternationalPriceByInquiry } from "../SupplyPrice/services";
import dayjs from "dayjs";
import { formatDateToTimestamp } from "@/utils/util";

const PACKAGING_OPTIONS = ["纸箱", "布卷", "木箱", "桶", "麻袋"];
const SPECIAL_CARGO_OPTIONS = ["液体", "锂电", "带电", "航材", "温控", "鲜活"];

// 初始欢迎消息的建议选项
const INITIAL_SUGGESTIONS = [
  "我需要从上海到莫斯科的空运报价",
  "深圳到洛杉矶，3件货物，总重50公斤，总体积0.5立方米，尺寸100*80*60cm",
  "北京到墨西哥，锂电产品，纸箱包装，要求本周发货",
  "广州到圣彼得堡，要求明日发货，需要保舱服务",
];

// 货物毛重的建议选项
const GROSS_WEIGHT_SUGGESTIONS = [
  "总重10kg",
  "每件重量为5kg、8kg、12kg",
  "第一件重量为20kg、第二件为15kg",
  "10kg、8kg、16kg",
];

// 货物体积的建议选项
const GROSS_VOLUME_SUGGESTIONS = [
  "总体积为1.25cbm",
  "每件体积为2cbm、1.5cbm、1.75cbm",
  "第一件体积为2cbm、第二件为1.5cbm",
  "2cbm、1.5cbm、1.75cbm",
];

// 货物尺寸的建议选项
const GROSS_SIZE_SUGGESTIONS = [
  "50cm × 30cm × 20cm",
  "第一件 50cm × 30cm × 20cm，第二件 60cm × 40cm × 30cm",
  "全为20cm × 30cm × 40cm",
];

const extractSuggestions = (fieldType: string): string[] => {
  let optionsToCheck: string[] = [];

  if (fieldType === "包装形式") {
    optionsToCheck = PACKAGING_OPTIONS;
  } else if (fieldType === "特殊货物") {
    optionsToCheck = SPECIAL_CARGO_OPTIONS;
  } else if (fieldType === "货物总毛重") {
    optionsToCheck = GROSS_WEIGHT_SUGGESTIONS;
  } else if (fieldType === "货物总体积") {
    optionsToCheck = GROSS_VOLUME_SUGGESTIONS;
  } else if (fieldType === "货物尺寸") {
    optionsToCheck = GROSS_SIZE_SUGGESTIONS;
  }

  return optionsToCheck;
};

// 统一的字段信息解析
const parseFieldInfoFromResponse = (content: string) => {
  const match = content.match(/<([^>]+)>/);
  if (!match) return null;

  const infoString = match[1];
  const infoItems = infoString.split("，");

  const parsedInfo: { [key: string]: string } = {};

  infoItems.forEach((item) => {
    const [key, value] = item.split("：");
    if (key && value !== undefined) {
      const trimmedKey = key.trim();
      const trimmedValue = value.trim();
      parsedInfo[trimmedKey] = trimmedValue;
    }
  });

  return parsedInfo;
};

// 基于解析结果,分析缺失信息（用于流程控制）
const analyzeCollectionInfo = (parsedInfo: { [key: string]: string }) => {
  const missingInfo: string[] = [];

  Object.entries(parsedInfo).forEach(([key, value]) => {
    if (value === "暂无") {
      missingInfo.push(key);
    }
  });

  return {
    allInfo: parsedInfo,
    missingInfo,
    hasAllInfo: missingInfo.length === 0,
  };
};

const AIQuotation: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedQuotation, setSelectedQuotation] = useState<any | null>(null);
  const [quotationModalVisible, setQuotationModalVisible] = useState(false);
  const [savedQuotations, setSavedQuotations] = useState<any[]>([]);
  const [initializing, setInitializing] = useState(true);
  // 上一次请求的完整message参数
  const [lastRequestMessages, setLastRequestMessages] = useState<Array<{
    role: string;
    content: any;
  }> | null>(null);
  // 上一次AI的响应
  const [lastAIResponse, setLastAIResponse] = useState<string | null>(null);
  // 提取的货运信息
  const [extractedShippingInfo, setExtractedShippingInfo] = useState<{
    allInfo: { [key: string]: string };
    missingInfo: string[];
    hasAllInfo: boolean;
  } | null>(null);
  // 当前正在收集的信息字段
  const [currentCollectingField, setCurrentCollectingField] = useState<
    string | null
  >(null);
  // 待收集的信息队列
  const [pendingFields, setPendingFields] = useState<string[]>([]);
  // 对话轮次计数器
  const [conversationRound, setConversationRound] = useState(1);
  // 是否需要重新初始化AI助手
  const [needReinitialize, setNeedReinitialize] = useState(false);
  // 是否是首次初始化
  const [isFirstInitialization, setIsFirstInitialization] = useState(true);
  // 当前活动面板
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<TextAreaRef>(null);
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.user);

  const hasAccess = user?.useridentity === 0;

  // 重置对话状态
  const resetConversationState = (keepHistory: boolean = true) => {
    setLastRequestMessages(null);
    setLastAIResponse(null);
    setExtractedShippingInfo(null);
    setCurrentCollectingField(null);
    setPendingFields([]);
    setInputValue("");

    if (keepHistory) {
      // 保留历史消息，增加对话轮次
      setConversationRound((prev) => prev + 1);
      setNeedReinitialize(true);
    } else {
      // 完全重置，清空所有消息
      setMessages([]);
      setConversationRound(1);
      setIsFirstInitialization(true);
      setNeedReinitialize(false);
    }
  };

  // 手动开启新对话
  const handleStartNewConversation = () => {
    // 检查是否有正在进行的对话或已有消息
    const hasActiveConversation =
      currentCollectingField ||
      extractedShippingInfo ||
      pendingFields.length > 0 ||
      isTyping ||
      messages.length > 0;

    if (hasActiveConversation) {
      Modal.confirm({
        title: "开启新对话",
        content: "开启新对话将清空所有聊天记录并重新开始。确定要继续吗？",
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          resetConversationState(false);
          message.success("已重新开始对话");
        },
      });
    } else {
      resetConversationState(false);
      message.success("已重新开始对话");
    }
  };

  useEffect(() => {
    const initializeAI = async () => {
      setInitializing(true);
      try {
        if (isFirstInitialization) {
          setMessages([]);
          setIsFirstInitialization(false);
        }

        // 调用DeepSeek API获取初始欢迎信息
        const response = await initializeAIAssistant();

        const content =
          response?.data?.data?.body?.choices?.[0]?.message?.content;
        if (content) {
          const welcomeMessage: Message = {
            id: `welcome-${conversationRound}`,
            content: content,
            sender: "ai",
            timestamp: new Date(),
            suggestions: INITIAL_SUGGESTIONS,
            isNewRound: needReinitialize, // 标记是否为新对话轮次
          };

          if (needReinitialize) {
            setMessages((prev) => [...prev, welcomeMessage]);
            setNeedReinitialize(false);
          } else {
            setMessages([welcomeMessage]);
          }
        } else {
          // 如果API调用失败，使用默认欢迎信息
          const fallbackMessage: Message = {
            id: `welcome-${conversationRound}`,
            content: "抱歉，AI助手初始化失败。请检查网络连接或API Key设置。",
            sender: "ai",
            timestamp: new Date(),
            suggestions: INITIAL_SUGGESTIONS,
            isNewRound: needReinitialize,
          };

          if (needReinitialize) {
            setMessages((prev) => [...prev, fallbackMessage]);
            setNeedReinitialize(false);
          } else {
            setMessages([fallbackMessage]);
          }
        }
      } catch (error) {
        console.error("初始化AI助手失败:", error);
        const fallbackMessage: Message = {
          id: `welcome-${conversationRound}`,
          content: "抱歉，AI助手初始化失败。请检查网络连接或API Key设置。",
          sender: "ai",
          timestamp: new Date(),
          suggestions: INITIAL_SUGGESTIONS,
          isNewRound: needReinitialize,
        };

        if (needReinitialize) {
          setMessages((prev) => [...prev, fallbackMessage]);
          setNeedReinitialize(false);
        } else {
          setMessages([fallbackMessage]);
        }
      } finally {
        setInitializing(false);
      }
    };

    // 只在首次加载或需要重新初始化时执行
    if (isFirstInitialization || needReinitialize) {
      initializeAI();
    }
  }, [needReinitialize, conversationRound, isFirstInitialization]);

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // 检查消息列表中是否有加载中的消息
  const hasLoadingMessage = messages.some((msg) => msg.isLoading);

  // 根据消息加载状态更新输入框禁用状态
  useEffect(() => {
    setIsTyping(hasLoadingMessage);
  }, [hasLoadingMessage]);

  const updateMessage = (
    content: string,
    id: string,
    suggestions?: string[]
  ) => {
    const aiResponse: Message = {
      id,
      content,
      sender: "ai",
      timestamp: new Date(),
      suggestions:
        suggestions && suggestions.length > 0 ? suggestions : undefined,
    };
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? aiResponse : msg))
    );
  };

  const setErrorMessage = (content: string, id: string) => {
    const errorMessage: Message = {
      id,
      content,
      sender: "ai",
      timestamp: new Date(),
    };
    setMessages((prev) =>
      prev.map((msg) => (msg.id === id ? errorMessage : msg))
    );
  };

  const removeLoadingMessage = (id: string) => {
    setMessages((prev) => prev.filter((msg) => msg.id !== id));
  };

  const mergeInfo = (
    currentInfo: Record<string, string>,
    newFieldInfo: Record<string, string>
  ) => {
    return {
      ...currentInfo,
      ...newFieldInfo,
    };
  };

  const updateExtractedInfo = (info: Record<string, string>) => {
    setExtractedShippingInfo({
      allInfo: info,
      missingInfo: Object.entries(info)
        .filter(([_, v]) => v === "暂无")
        .map(([k]) => k),
      hasAllInfo: Object.values(info).every((v) => v !== "暂无"),
    });
  };

  const handleFinalQuotation = async (
    info: Record<string, string>,
    loadingId: string
  ) => {
    const formattedInfo = formatFinalShippingInfo(info);
    const summaryMessage: Message = {
      id: loadingId,
      content: `🎉 货运信息收集完成！\n\n正在进行报价查询,请稍后...`,
      sender: "ai",
      timestamp: new Date(),
    };
    setMessages((prev) =>
      prev.map((msg) => (msg.id === loadingId ? summaryMessage : msg))
    );

    try {
      const converted = await performPortConversion(formattedInfo);
      console.log("港口转换完成，转换后的对象:", converted);
      const quotations = await fetchQuotationList(converted);
      console.log("报价获取完成，报价数量:", quotations?.length || 0);
    } catch (error) {
      console.error("港口转换或获取报价失败:", error);
      const errorMsg: Message = {
        id: Date.now().toString(),
        content: "抱歉，获取报价时出现错误，请稍后重试。",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMsg]);
    }
  };

  const getAIResponse = async (userMessage: string) => {
    const now = Date.now();

    const newUserMessage: Message = {
      id: now.toString(),
      content: userMessage,
      sender: "user",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, newUserMessage]);

    const loadingMessageId = (now + 1).toString();
    const loadingMessage: Message = {
      id: loadingMessageId,
      content: "",
      sender: "ai",
      timestamp: new Date(),
      isLoading: true,
    };
    setMessages((prev) => [...prev, loadingMessage]);

    try {
      const filteredMessages = messages.filter((msg) => !msg.isLoading);

      // 找到最后一个欢迎消息的索引
      let lastWelcomeIndex = -1;
      for (let i = filteredMessages.length - 1; i >= 0; i--) {
        if (filteredMessages[i].id.startsWith("welcome-")) {
          lastWelcomeIndex = i;
          break;
        }
      }

      // 从最后一个欢迎消息开始获取当前轮次的消息
      const currentRoundMessages = (
        lastWelcomeIndex >= 0
          ? filteredMessages.slice(lastWelcomeIndex)
          : filteredMessages
      ).map((msg) => ({
        role: msg.sender === "user" ? "user" : "assistant",
        content: msg.content,
      }));

      const response = await sendMessageToAI(
        userMessage,
        currentRoundMessages,
        lastRequestMessages || undefined,
        lastAIResponse || undefined
      );

      const aiContent =
        response?.data?.data?.body?.choices?.[0]?.message?.content;

      if (!aiContent) {
        return setErrorMessage(
          "抱歉，我现在无法处理您的请求，请稍后再试。",
          loadingMessageId
        );
      }

      // 响应内容处理
      const parsedFieldInfo = parseFieldInfoFromResponse(aiContent);

      // 计算最新的完整信息（用于后续流程控制）
      let latestCompleteInfo = extractedShippingInfo?.allInfo || {};

      // 如果解析到了字段信息，更新存储的数据
      if (parsedFieldInfo && Object.keys(parsedFieldInfo).length > 0) {
        if (currentCollectingField) {
          // 正在收集某字段，合并信息
          const updatedInfo = mergeInfo(
            extractedShippingInfo?.allInfo || {},
            parsedFieldInfo
          );

          // 特殊处理ETD字段
          if (currentCollectingField === "要求ETD") {
            const etdValue = parsedFieldInfo["要求ETD"];
            if (etdValue === "无" || etdValue === "暂无") {
              updatedInfo["保舱"] = "无";
            }
          }
          latestCompleteInfo = updatedInfo;

          updateExtractedInfo(updatedInfo);
          console.log("更新字段信息:", parsedFieldInfo);
          console.log("合并后的完整信息:", updatedInfo);
        } else {
          const currentInfo = extractedShippingInfo?.allInfo || {};
          const updatedInfo = { ...currentInfo, ...parsedFieldInfo };

          latestCompleteInfo = updatedInfo;

          updateExtractedInfo(updatedInfo);
          console.log("首次解析字段信息:", parsedFieldInfo);
          console.log("更新后的完整信息:", updatedInfo);
        }
      }

      if (aiContent.includes("Collection completed")) {
        const parsedInfo = parseFieldInfoFromResponse(aiContent);

        if (!parsedInfo) {
          removeLoadingMessage(loadingMessageId);
          return;
        }

        const extractedInfo = analyzeCollectionInfo(parsedInfo);

        if (currentCollectingField) {
          console.log("使用最新完整信息:", latestCompleteInfo);

          // 检查哪些字段仍然是"暂无"状态，这些才是需要继续收集的字段
          let fieldsToProcess = Object.entries(latestCompleteInfo)
            .filter(([_, value]) => value === "暂无")
            .map(([key]) => key);

          // 如果ETD为无或暂无，则从待处理字段中移除保舱字段
          if (
            latestCompleteInfo["要求ETD"] === "无" ||
            latestCompleteInfo["要求ETD"] === "暂无"
          ) {
            fieldsToProcess = fieldsToProcess.filter((f) => f !== "保舱");
          }

          console.log("Collection completed - 待处理字段:", fieldsToProcess);

          setPendingFields(fieldsToProcess);
          setCurrentCollectingField(null);

          if (fieldsToProcess.length > 0) {
            return startSingleFieldCollection(
              fieldsToProcess[0],
              latestCompleteInfo,
              loadingMessageId
            );
          } else {
            return handleFinalQuotation(latestCompleteInfo, loadingMessageId);
          }
        }

        if (extractedInfo.missingInfo.length > 0) {
          setExtractedShippingInfo(extractedInfo);
          setPendingFields(extractedInfo.missingInfo);
          return startSingleFieldCollection(
            extractedInfo.missingInfo[0],
            extractedInfo.allInfo,
            loadingMessageId
          );
        }

        // 所有信息已收集完毕
        return handleFinalQuotation(latestCompleteInfo, loadingMessageId);
      }

      // 检查是否正在收集特定字段并需要添加建议选项
      let suggestions: string[] = [];
      if (currentCollectingField) {
        if (
          currentCollectingField === "包装形式" ||
          currentCollectingField === "特殊货物"
        ) {
          suggestions = extractSuggestions(currentCollectingField);
        }
      }

      updateMessage(aiContent, loadingMessageId, suggestions);
      setLastAIResponse(aiContent);
      if (response.sentMessages) {
        setLastRequestMessages(response.sentMessages);
      }
    } catch (error) {
      console.error("调用DeepSeek API失败:", error);
      setErrorMessage(
        "抱歉，我现在无法处理您的请求，请检查网络连接或API Key设置。",
        loadingMessageId
      );
    }
  };

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      getAIResponse(inputValue);
      setInputValue("");
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 开始收集单个字段信息
  const startSingleFieldCollection = async (
    targetField: string,
    collectedInfo: { [key: string]: string },
    existingLoadingMessageId?: string
  ) => {
    try {
      setCurrentCollectingField(targetField);

      // 生成单个字段收集的系统提示词
      const fieldRequest = generateSingleFieldCollectionRequest(
        targetField,
        collectedInfo
      );
      setLastRequestMessages([fieldRequest]);
      setLastAIResponse(null);

      const response = await sendMessageToAI("", [], [fieldRequest]);

      const aiContent =
        response?.data?.data?.body?.choices?.[0]?.message?.content;
      if (aiContent) {
        // 检查是否需要添加建议选项
        let suggestions: string[] = [];
        if (targetField === "包装形式" || targetField === "特殊货物") {
          // 对于包装形式和特殊货物，使用预设选项
          suggestions = extractSuggestions(targetField);
        } else if (
          targetField === "货物总毛重" ||
          targetField === "货物总体积" ||
          targetField === "货物尺寸"
        ) {
          suggestions = extractSuggestions(targetField);
        }

        const aiResponse: Message = {
          id: existingLoadingMessageId || Date.now().toString(),
          content: aiContent,
          sender: "ai",
          timestamp: new Date(),
          suggestions: suggestions.length > 0 ? suggestions : undefined,
        };

        if (existingLoadingMessageId) {
          setMessages((prev) =>
            prev.map((msg) =>
              msg.id === existingLoadingMessageId ? aiResponse : msg
            )
          );
        } else {
          setMessages((prev) => [...prev, aiResponse]);
        }

        if (response.sentMessages) {
          setLastRequestMessages(response.sentMessages);
        }
        setLastAIResponse(aiContent);
      }
    } catch (error) {
      console.error("开始收集字段信息失败:", error);
    }
  };

  // 港口转换函数（类似快捷询价的第二轮对话）
  const performPortConversion = async (formattedInfo: string) => {
    try {
      // 生成港口转换的系统提示词
      const portConvertPrompt = generatePortConvertSystemPrompt(formattedInfo);

      const messages = [
        {
          role: "user",
          content: portConvertPrompt,
        },
      ];

      const response = await chatCompletions(messages);

      const aiResponse =
        response?.data?.data?.body?.choices?.[0]?.message?.content;
      if (aiResponse) {
        const convertedData = parsePortConvertResponse(aiResponse);
        return convertedData;
      } else {
        throw new Error("港口转换AI响应格式错误");
      }
    } catch (error) {
      console.error("港口转换失败:", error);
      throw error;
    }
  };

  const fetchQuotationList = async (convertedData: ConvertedShippingData) => {
    try {
      console.log("开始获取报价列表，转换后的数据:", convertedData);
      const queryParams = {
        ...convertedData,
        unloadingport: Array.isArray(convertedData.unloadingport)
          ? convertedData.unloadingport.join(",")
          : convertedData.unloadingport,
        specialcargo: Array.isArray(convertedData.specialcargo)
          ? convertedData.specialcargo.join(",")
          : convertedData.specialcargo,
        shipmentdate: convertedData.shipmentdate
          ? formatDateToTimestamp(dayjs(convertedData?.shipmentdate))
          : undefined,
        userid: user?.userid,
        departmentid: user?.departmentid,
        packagetype:
          (convertedData.packagetype === "纸箱"
            ? "散箱"
            : convertedData.packagetype) || "其他",
      };

      console.log("报价查询参数:", queryParams);

      const response = await getInternationalPriceByInquiry(queryParams);

      if (response.data?.resultCode === 200) {
        const quotationList = response.data.data || [];
        console.log("获取到的报价列表:", quotationList);

        displayQuotationList(quotationList);

        return quotationList;
      } else {
        throw new Error(response.data?.message || "获取报价失败");
      }
    } catch (error) {
      console.error("获取报价列表失败:", error);
      throw error;
    }
  };

  const displayQuotationList = (quotationList: any[]) => {
    const quotationMessage: Message = {
      id: Date.now().toString(),
      content:
        quotationList.length > 0
          ? `为您找到 ${quotationList.length} 个报价方案：`
          : "抱歉，暂未找到符合条件的报价方案。",
      sender: "ai",
      timestamp: new Date(),
      quotationList: quotationList,
      isQuotationList: true,
    };

    setMessages((prev) => [...prev, quotationMessage]);
    setTimeout(() => {
      resetConversationState(true);
    }, 3000);
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (inputValue.trim()) {
      setInputValue(inputValue.trim() + "，" + suggestion);
    } else {
      setInputValue(suggestion);
    }
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  const handleQuotationSelect = (quotation: any) => {
    setSelectedQuotation(quotation);
    setQuotationModalVisible(true);
  };

  const handleSaveQuotation = (quotation: any) => {
    const quotationId = quotation.priceid || quotation.id;

    const isAlreadySaved = savedQuotations.some(
      (q) => (q.priceid || q.id) === quotationId
    );

    if (!isAlreadySaved) {
      setSavedQuotations((prev) => [...prev, quotation]);
      message.success("报价已保存到我的报价列表");
    } else {
      setSavedQuotations((prev) =>
        prev.filter((q) => (q.priceid || q.id) !== quotationId)
      );
      message.success("报价已从我的报价列表中移除");
    }
  };

  const handleAddToSavedQuotes = () => {
    if (selectedQuotation) {
      handleSaveQuotation(selectedQuotation);
      setQuotationModalVisible(false);
    }
  };

  useEffect(() => {
    if (user && !hasAccess) {
      message.error("您没有权限访问此页面");
      navigate("/");
    } else if (!user) {
      navigate("/login");
    }
  }, [hasAccess, navigate, user]);

  if (!hasAccess) {
    return <NoAccessResult />;
  }

  return (
    <div className="ai-quotation-container">
      <div className="ai-quotation-content">
        <div className="ai-interface-container">
          {/* 聊天面板 */}
          <ChatPanel
            messages={messages}
            inputValue={inputValue}
            isTyping={isTyping}
            initializing={initializing}
            inputRef={inputRef}
            messagesEndRef={messagesEndRef}
            handleSendMessage={handleSendMessage}
            handleKeyDown={handleKeyDown}
            setInputValue={setInputValue}
            handleSuggestionClick={handleSuggestionClick}
            handleQuotationSelect={handleQuotationSelect}
            onSaveQuotation={handleSaveQuotation}
            savedQuotations={savedQuotations}
            onStartNewConversation={handleStartNewConversation}
          />

          {/* 报价列表面板 */}
          <QuotationList
            savedQuotations={savedQuotations}
            handleQuotationSelect={handleQuotationSelect}
          />
        </div>
      </div>

      {/* 报价单详情模态框 */}
      <QuotationDetailModal
        visible={quotationModalVisible}
        selectedQuotation={selectedQuotation}
        onClose={() => setQuotationModalVisible(false)}
        onSave={handleAddToSavedQuotes}
        onGenerate={() => {
          message.success("正式报价单已生成");
          setQuotationModalVisible(false);
        }}
      />
    </div>
  );
};

export default AIQuotation;
