import React, { useState } from "react";
import { DatePicker, Button, Space, Divider } from "antd";
import dayjs from "dayjs";
import { formatDateRangeToTimestamp } from "@/utils/util";

const { RangePicker } = DatePicker;

interface DateRangeFilterProps {
  setSelectedKeys: (selectedKeys: string[]) => void;
  confirm: () => void;
  clearFilters: () => void;
  startParamName?: string;
  endParamName?: string;
}

/**
 * 日期范围筛选组件
 * @param setSelectedKeys 设置选中的键
 * @param confirm 确认筛选
 * @param clearFilters 清除筛选
 * @param startParamName 开始日期参数名，默认为 "leftupdatetime"
 * @param endParamName 结束日期参数名，默认为 "rightupdatetime"
 */
const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  setSelectedKeys,
  confirm,
  clearFilters,
  startParamName = "leftupdatetime",
  endParamName = "rightupdatetime",
}) => {
  const [dates, setDates] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([
    null,
    null,
  ]);

  const handleRangeChange = (
    dates: [dayjs.Dayjs | null, dayjs.Dayjs | null],
    _dateStrings: [string, string]
  ) => {
    setDates(dates);

    if (dates && dates[0] && dates[1]) {
      // 使用日期范围格式化函数，起始时间为00:00:00，结束时间为23:59:59
      const { startTimestamp, endTimestamp } = formatDateRangeToTimestamp(
        dates[0],
        dates[1]
      );

      // 设置筛选值，格式为 ["startParamName=开始时间戳", "endParamName=结束时间戳"]
      setSelectedKeys([
        `${startParamName}=${startTimestamp}`,
        `${endParamName}=${endTimestamp}`,
      ]);
    } else {
      setSelectedKeys([]);
    }
  };

  // 快捷筛选功能
  const handleQuickFilter = (days: number) => {
    const today = dayjs();
    const targetDate = today.subtract(days, "day");

    // 30天前到目标日期的范围
    const startDate = today.subtract(30, "day");
    const endDate = targetDate;

    const newDates: [dayjs.Dayjs, dayjs.Dayjs] = [startDate, endDate];
    setDates(newDates);

    const { startTimestamp, endTimestamp } = formatDateRangeToTimestamp(
      startDate,
      endDate
    );

    setSelectedKeys([
      `${startParamName}=${startTimestamp}`,
      `${endParamName}=${endTimestamp}`,
    ]);
    confirm();
  };

  const handleConfirm = () => {
    confirm();
  };

  const handleClear = () => {
    setDates([null, null]);
    setSelectedKeys([]);
    clearFilters();
    confirm();
  };

  return (
    <div style={{ padding: 8, minWidth: 280 }}>
      <Space direction="vertical" style={{ width: "100%" }}>
        {/* 快捷筛选按钮 */}
        <div>
          <div style={{ marginBottom: 8, fontSize: 12, color: "#666" }}>
            快捷筛选（30天前截至到指定日期）
          </div>
          <Space wrap>
            <Button
              size="small"
              onClick={() => handleQuickFilter(1)}
              style={{ fontSize: 11 }}
            >
              1天前
            </Button>
            <Button
              size="small"
              onClick={() => handleQuickFilter(3)}
              style={{ fontSize: 11 }}
            >
              3天前
            </Button>
            <Button
              size="small"
              onClick={() => handleQuickFilter(7)}
              style={{ fontSize: 11 }}
            >
              7天前
            </Button>
          </Space>
        </div>

        <Divider style={{ margin: "8px 0" }} />

        {/* 自定义日期范围选择 */}
        <div>
          <div style={{ marginBottom: 8, fontSize: 12, color: "#666" }}>
            自定义日期范围
          </div>
          <RangePicker
            value={dates}
            onChange={handleRangeChange as any}
            style={{ width: "100%" }}
            size="small"
          />
        </div>

        <Space>
          <Button type="primary" onClick={handleConfirm} size="small">
            确定
          </Button>
          <Button onClick={handleClear} size="small">
            重置
          </Button>
        </Space>
      </Space>
    </div>
  );
};

export default DateRangeFilter;
