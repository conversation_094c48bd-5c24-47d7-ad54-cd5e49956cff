import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { addPackageType, updatePackageType } from "../services";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  queryPackageTypeList: () => void;
}

const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryPackageTypeList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updatePackageType(values)
            : await addPackageType(values);
        const { data } = res;
        if (data.resultCode === 200) {
          queryPackageTypeList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t("dataManagement.packageTypeManagement.messages.updateSuccess")
              : t("dataManagement.packageTypeManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t("dataManagement.packageTypeManagement.messages.updateFailed")
              : t("dataManagement.packageTypeManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.packageTypeManagement.modal.title.add")
          : t("dataManagement.packageTypeManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t("dataManagement.packageTypeManagement.modal.fields.id")}
            name="packagetypeid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.packageTypeManagement.modal.fields.packageName"
          )}
          name="packagename"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.packageTypeManagement.modal.validation.packageNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.packageTypeManagement.modal.validation.packageNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.packageTypeManagement.modal.placeholders.packageName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
