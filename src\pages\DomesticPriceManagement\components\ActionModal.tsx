import React, { useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Col,
  Select,
} from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { addDomesticPrice, updateDomesticPrice } from "../services";
import "./index.less";
import { useAppSelector } from "@/store/hooks";
import useBaseData from "@/hooks/useBaseData";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  queryDomesticPriceList: () => void;
}

const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryDomesticPriceList,
}) => {
  const { t } = useTranslation();
  const { user } = useAppSelector((state) => state.user);
  const {
    shipmentPlaceOptions,
    ShippingProvinceOptions,
    portOptions,
    loadShipmentPlaces,
    loadPorts,
  } = useBaseData();

  useEffect(() => {
    loadShipmentPlaces();
    loadPorts();
  }, []);
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const formattedValues = {
          ...values,
          destination: values.destination.join(","),
          userid: user?.userid,
        };
        const res =
          method === "edit"
            ? await updateDomesticPrice(formattedValues)
            : await addDomesticPrice(formattedValues);
        const { data } = res;
        if (data.resultCode === 200) {
          queryDomesticPriceList();
          setIsModalOpen(false);
          message.success(
            t(
              `domesticPriceManagement.messages.${
                method === "edit" ? "editSuccess" : "addSuccess"
              }`
            )
          );
        } else {
          message.error(
            t(
              `domesticPriceManagement.messages.${
                method === "edit" ? "editFailed" : "addFailed"
              }`
            )
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("domesticPriceManagement.actionModal.title.add")
          : t("domesticPriceManagement.actionModal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      className="domestic-price-modal"
      destroyOnClose
    >
      <Form
        name="basic"
        layout="vertical"
        form={form}
        className="domestic-price-form"
      >
        <Form.Item
          label="ID"
          name="domesticpriceid"
          style={{ display: "none" }}
        >
          <Input disabled />
        </Form.Item>

        <div className="price-section">
          <div className="section-title">
            {t("domesticPriceManagement.actionModal.sections.basicInfo")}
          </div>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.shippingPlace"
                )}
                name="shippingplace"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.shippingPlaceRequired"
                    ),
                  },
                ]}
              >
                <Select
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.selectShippingPlace"
                  )}
                  showSearch
                  optionFilterProp="label"
                  options={shipmentPlaceOptions}
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.shippingProvince"
                )}
                name="shippingprovince"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.shippingProvinceRequired"
                    ),
                  },
                  // { max: 100, message: "发货省份不能超过100个字符!" },
                ]}
              >
                {/* <Input placeholder="请输入发货省份" /> */}
                <Select
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.selectShippingProvince"
                  )}
                  showSearch
                  optionFilterProp="label"
                  options={ShippingProvinceOptions}
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.destination"
                )}
                name="destination"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.destinationRequired"
                    ),
                  },
                ]}
              >
                <Select
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.selectDestination"
                  )}
                  showSearch
                  optionFilterProp="label"
                  options={portOptions}
                  mode="multiple"
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <div className="price-section">
          <div className="section-title">
            {t("domesticPriceManagement.actionModal.sections.priceInfo")}
          </div>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.volumePrice"
                )}
                name="volumeprice"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.volumePriceRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={3}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputVolumePrice"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.yuanPerCubicMeter"
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.weightPrice"
                )}
                name="weightprice"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.weightPriceRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={3}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputWeightPrice"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.yuanPerKg"
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.minCharge"
                )}
                name="minicharge"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.minChargeRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={3}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputMinCharge"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.yuan"
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.deliveryFee"
                )}
                name="deliveryfee"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.deliveryFeeRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  precision={3}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputDeliveryFee"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.yuan"
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.logistics"
                )}
                name="logistics"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.logisticsRequired"
                    ),
                  },
                  {
                    max: 50,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.logisticsMaxLength"
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputLogistics"
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <div className="price-section">
          <div className="section-title">
            {t("domesticPriceManagement.actionModal.sections.agingInfo")}
          </div>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.fastestAging"
                )}
                name="fastestaging"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.fastestAgingRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={1}
                  precision={0}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputFastestAging"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.days"
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={t(
                  "domesticPriceManagement.actionModal.fields.slowestAging"
                )}
                name="slowestaging"
                rules={[
                  {
                    required: true,
                    message: t(
                      "domesticPriceManagement.actionModal.validation.slowestAgingRequired"
                    ),
                  },
                ]}
                className="form-item-with-unit"
              >
                <InputNumber
                  min={1}
                  precision={0}
                  style={{ width: "100%" }}
                  placeholder={t(
                    "domesticPriceManagement.actionModal.placeholders.inputSlowestAging"
                  )}
                  addonAfter={t(
                    "domesticPriceManagement.actionModal.units.days"
                  )}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>
    </Modal>
  );
};

export default ActionModal;
