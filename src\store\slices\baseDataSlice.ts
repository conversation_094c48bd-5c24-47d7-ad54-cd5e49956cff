import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// 航司数据类型
export interface Airline {
  airlineid: number;
  cnairlinename: string;
  enairlinename: string;
}

// 港口数据类型
export interface Port {
  portid: number;
  cnportname: string;
  enportname: string;
}

// 包装类型数据类型
export interface PackageType {
  packagetypeid: number;
  packagename: string;
}

// 特殊货物数据类型
export interface SpecialItem {
  specialitemsid: number;
  specialitemsname: string;
}

// 发货地数据类型
export interface ShipmentPlace {
  placeid: number;
  enplace: string;
  enprovince: string;
}

// 供应商数据类型
export interface Supplier {
  supplierid: number;
  suppliername: string;
}

// 其他费用数据类型
export interface MiscellaneousFee {
  otherfeesid: number;
  feesname: string;
}

// 基础数据状态类型
export interface BaseDataState {
  airlines: Airline[];
  ports: Port[];
  packageTypes: PackageType[];
  specialItems: SpecialItem[];
  shipmentPlaces: ShipmentPlace[];
  suppliers: Supplier[];
  miscellaneousFees: MiscellaneousFee[];
}

const initialState: BaseDataState = {
  airlines: [],
  ports: [],
  packageTypes: [],
  specialItems: [],
  shipmentPlaces: [],
  suppliers: [],
  miscellaneousFees: [],
};

const baseDataSlice = createSlice({
  name: "baseData",
  initialState,
  reducers: {
    // 设置航司数据
    setAirlines: (state, action: PayloadAction<Airline[]>) => {
      state.airlines = action.payload;
    },
    // 设置港口数据
    setPorts: (state, action: PayloadAction<Port[]>) => {
      state.ports = action.payload;
    },
    // 设置包装类型数据
    setPackageTypes: (state, action: PayloadAction<PackageType[]>) => {
      state.packageTypes = action.payload;
    },
    // 设置特殊货物数据
    setSpecialItems: (state, action: PayloadAction<SpecialItem[]>) => {
      state.specialItems = action.payload;
    },
    // 设置发货地数据
    setShipmentPlaces: (state, action: PayloadAction<ShipmentPlace[]>) => {
      state.shipmentPlaces = action.payload;
    },
    // 设置供应商数据
    setSuppliers: (state, action: PayloadAction<Supplier[]>) => {
      state.suppliers = action.payload;
    },
    setMiscellaneousFees: (
      state,
      action: PayloadAction<MiscellaneousFee[]>
    ) => {
      state.miscellaneousFees = action.payload;
    },
  },
});

export const {
  setAirlines,
  setPorts,
  setPackageTypes,
  setSpecialItems,
  setShipmentPlaces,
  setSuppliers,
  setMiscellaneousFees,
} = baseDataSlice.actions;

export default baseDataSlice.reducer;
