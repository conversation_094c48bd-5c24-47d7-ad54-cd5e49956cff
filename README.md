# 报价管理系统

一个基于 React + TypeScript + Ant Design 构建的现代化货运报价管理系统，集成了AI智能报价、价格管理、询价管理等核心功能。

## 🚀 项目特色

- **AI智能报价**: 集成DeepSeek AI，支持自然语言交互式报价查询
- **3D可视化**: 使用Three.js打造科幻风格的3D交互界面
- **多角色权限**: 支持6种用户角色的精细化权限管理
- **国际化支持**: 完整的中英文双语支持
- **实时数据**: 基于Redux的状态管理，支持实时数据更新

## 📋 功能模块

### 🤖 AI智能报价

- **智能对话**: 支持自然语言描述货运需求
- **信息收集**: AI自动收集缺失的货运信息
- **港口转换**: 智能识别和转换港口信息
- **报价查询**: 自动匹配最优报价方案
- **3D界面**: 科幻风格的宇宙飞船控制台界面

### 📊 询价管理

- **询价单管理**: 创建、编辑、删除询价单
- **快捷提取**: AI自动从邮件内容提取询价信息
- **状态跟踪**: 未报价、已成交、未成交状态管理
- **批量操作**: 支持批量处理询价单
- **权限控制**: 基于角色的数据访问控制

### 💰 价格管理

- **国际价格**: 航空货运国际价格管理
- **国内价格**: 国内货运价格管理
- **实时编辑**: 支持表格内直接编辑价格
- **筛选排序**: 多维度筛选和排序功能

### 🔍 供应价格筛选

- **智能筛选**: 基于货运需求智能匹配价格
- **多条件筛选**: 支持航司、港口、时间等多维度筛选
- **价格对比**: 直观的价格卡片展示和对比
- **报价生成**: 一键生成正式报价单

### 🛠️ 基础数据管理

- **航司管理**: 航空公司信息维护
- **港口管理**: 全球港口信息管理
- **包装类型**: 货物包装类型配置
- **特殊物品**: 特殊货物类型管理
- **发货地管理**: 发货地点信息维护

### 👥 用户权限管理

- **多角色支持**: 6种用户角色精细化权限控制
- **组织管理**: 部门和用户组织架构管理
- **权限配置**: 灵活的功能权限配置
- **数据隔离**: 基于角色的数据访问隔离

## 🏗️ 技术架构

### 前端技术栈

- **React 18**: 现代化React框架
- **TypeScript**: 类型安全的JavaScript超集
- **Ant Design 5**: 企业级UI组件库
- **Redux Toolkit**: 状态管理解决方案
- **React Router 7**: 客户端路由管理
- **Three.js**: 3D图形渲染引擎
- **i18next**: 国际化解决方案

### 开发工具

- **CRACO**: Create React App配置覆盖
- **Less**: CSS预处理器
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **MSW**: API模拟和测试

### 核心依赖

```json
{
  "react": "^18.3.1",
  "typescript": "^4.9.5",
  "antd": "^5.24.7",
  "@reduxjs/toolkit": "^2.6.1",
  "react-router-dom": "^7.3.0",
  "three": "^0.150.1",
  "@react-three/fiber": "^8.13.0",
  "i18next": "^25.2.1",
  "axios": "^1.8.3",
  "dayjs": "^1.11.13"
}
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 启动开发服务器

```bash
npm start
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm test
```

### 代码检查

```bash
npm run lint
```

### 打包分析

```bash
npm run analyze
```

## 👥 用户角色权限

| 角色ID | 角色名称     | 权限描述     | 可访问功能                       |
| ------ | ------------ | ------------ | -------------------------------- |
| 0      | 普通用户     | 基础报价查询 | AI智能报价、个人报价             |
| 1      | 询价业务员   | 询价业务处理 | 询价管理、供应价格筛选、价格查看 |
| 2      | 价格业务员   | 价格数据维护 | 价格管理、基础数据管理           |
| 3      | 询价部门主管 | 询价业务管理 | 询价管理、价格查看、团队数据     |
| 4      | 价格部门主管 | 价格业务管理 | 价格管理、基础数据管理、团队数据 |
| 5      | 超级管理员   | 系统全权限   | 基础数据管理、组织管理           |

## 🌐 国际化支持

系统支持中英文双语切换：

- 中文 (zh-CN): 简体中文界面
- 英文 (en-US): 英文界面

## 🎨 界面特色

### AI智能报价界面

- 科幻风格的宇宙飞船控制台设计
- 3D地球背景和动态星光效果
- 半透明磨砂玻璃风格的聊天界面
- 智能建议和快捷操作按钮

### 数据管理界面

- 现代化的表格设计，支持内联编辑
- 多维度筛选和排序功能
- 响应式布局，适配各种屏幕尺寸
- 直观的数据可视化展示

## 🔧 配置说明

### 环境变量

创建 `.env` 文件配置环境变量：

```env
REACT_APP_API_BASE_URL=http://localhost:8080/api
REACT_APP_DEEPSEEK_API_KEY=your_deepseek_api_key
```

### API配置

系统支持多个后端服务配置：

- 主要API服务：用户认证、业务数据
- DeepSeek AI服务：智能报价功能

## 📱 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 📖 详细功能说明

### AI智能报价系统

AI智能报价是本系统的核心创新功能，采用DeepSeek大语言模型，为用户提供自然语言交互的报价体验：

#### 核心特性

- **自然语言理解**: 用户可以用自然语言描述货运需求，AI会智能理解并提取关键信息
- **智能信息收集**: 当信息不完整时，AI会主动询问缺失的关键信息
- **港口智能转换**: 自动识别和转换港口名称，支持中英文、简称、全称等多种格式
- **实时报价匹配**: 基于收集的信息自动匹配最优报价方案
- **3D科幻界面**: 独特的宇宙飞船控制台设计，提供沉浸式的用户体验

#### 技术实现

- 集成DeepSeek API进行自然语言处理
- 使用Three.js渲染3D地球和星空背景
- 实现多轮对话状态管理
- 支持结构化信息提取和展示

### 询价管理系统

完整的询价生命周期管理，支持从询价创建到成交跟踪的全流程：

#### 主要功能

- **询价单创建**: 支持手动创建和AI辅助创建
- **邮件信息提取**: AI自动从邮件内容中提取货运信息
- **多目的港处理**: 智能识别并创建多条询价记录
- **状态管理**: 未报价、已成交、未成交状态跟踪
- **权限控制**: 基于用户角色的数据访问控制

#### 特色功能

- **快捷提取**: 一键从邮件内容提取询价信息
- **批量操作**: 支持批量编辑和删除
- **智能筛选**: 多维度筛选和搜索功能

### 价格管理系统

专业的货运价格管理平台，支持国内外价格的统一管理：

#### 国际价格管理

- **航线价格**: 支持全球航线价格管理
- **梯度定价**: M价、N价、Q45-Q1000等多级价格体系
- **特殊费用**: 包装费、特殊物品费用配置
- **密度限制**: 货物密度范围和尺寸限制设置
- **实时编辑**: 表格内直接编辑，即时保存

#### 国内价格管理

- **省际运输**: 覆盖全国省际货运价格
- **物流配送**: 支持多种物流方式价格配置
- **区域定价**: 基于发货地和目的地的区域化定价

### 供应价格筛选

智能的价格匹配和筛选系统，帮助用户快速找到最优报价：

#### 筛选功能

- **多维度筛选**: 航司、港口、时间、价格等多重筛选条件
- **智能匹配**: 基于货运需求自动匹配合适的价格方案
- **价格对比**: 直观的卡片式价格展示和对比
- **时效排序**: 支持按价格、时效等多种排序方式

#### 报价生成

- **一键报价**: 选择价格方案后一键生成正式报价
- **自定义模板**: 支持自定义报价单模板

### 基础数据管理

完善的基础数据维护体系，确保系统数据的准确性和完整性：

#### 数据类型

- **航司信息**: 全球航空公司信息维护
- **港口数据**: 国际港口和机场信息管理
- **包装类型**: 货物包装方式配置
- **特殊物品**: 特殊货物类型和限制规则
- **发货地点**: 发货地区域信息管理

#### 管理功能

- **同步更新**: 基础数据变更自动同步到相关模块

## 🔒 安全特性

### 用户认证与授权

- **JWT令牌**: 基于JWT的无状态身份认证
- **角色权限**: 细粒度的角色权限控制系统
- **数据隔离**: 基于用户角色的数据访问隔离
- **会话管理**: 安全的会话超时和自动登出

### 数据安全

- **输入验证**: 前后端双重数据验证
- **XSS防护**: 防止跨站脚本攻击
- **CSRF保护**: 跨站请求伪造防护
- **数据加密**: 敏感数据传输加密

## 🛠️ 开发指南

### 项目结构

```
src/
├── components/          # 公共组件
│   ├── filters/        # 筛选组件
│   ├── TableCom/       # 表格组件
│   └── ThreeScene/     # 3D场景组件
├── pages/              # 页面组件
│   ├── AIQuotation/    # AI智能报价
│   ├── Quotation/      # 询价管理
│   ├── InternationalPriceManagement/ # 国际价格管理
│   └── ...
├── services/           # API服务
├── store/              # Redux状态管理
├── hooks/              # 自定义Hooks
├── utils/              # 工具函数
├── i18n/               # 国际化配置
└── router/             # 路由配置
```

### 代码规范

- **TypeScript**: 严格的类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理
- **Conventional Commits**: 规范化提交信息

## 📊 性能优化

### 前端优化

- **代码分割**: 基于路由的懒加载
- **组件缓存**: React.memo优化组件渲染
- **状态管理**: Redux Toolkit高效状态管理
- **资源压缩**: Webpack自动压缩和优化

### 用户体验优化

- **加载状态**: 全局Loading和骨架屏
- **错误处理**: 友好的错误提示和恢复机制

**报价管理系统** - 让货运报价更智能、更高效！
