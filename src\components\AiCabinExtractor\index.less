.ai-cabin-extractor-modal {
  .ai-cabin-extractor-content {
    .input-section {
      margin-bottom: 24px;

      .input-label {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 8px;
      }

      .input-textarea {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.2s;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus {
          border-color: #1797e1;
          box-shadow: 0 0 0 2px rgba(23, 151, 225, 0.2);
        }
      }
    }

    .progress-section {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 6px;
      border: 1px solid #e6f0fa;

      .progress-label {
        font-size: 14px;
        font-weight: 500;
        color: #1797e1;
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        &::before {
          content: "";
          margin-right: 6px;
          animation: flash 1s infinite;
        }
      }

      .ant-progress {
        .ant-progress-text {
          color: #1797e1;
          font-weight: 500;
        }
      }
    }

    .tips-section {
      background: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      border-left: 4px solid #1797e1;

      .tips-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        &::before {
          content: "";
          margin-right: 6px;
        }
      }

      .tips-list {
        margin: 0;
        padding-left: 16px;
        color: #595959;

        li {
          font-size: 13px;
          line-height: 1.6;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      display: flex;
      align-items: center;

      &::before {
        content: "";
        margin-right: 8px;
      }
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
  }
}

.ai-cabin-extractor-modal {
  .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s;

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1797e1 0%, #40a9ff 100%);
      border: none;
      box-shadow: 0 2px 4px rgba(23, 151, 225, 0.3);

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1797e1 100%);
        box-shadow: 0 4px 8px rgba(23, 151, 225, 0.4);
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }

      &.ant-btn-loading {
        background: #1797e1;
        transform: none;
      }
    }

    &.ant-btn-default {
      &:hover {
        border-color: #1797e1;
        color: #1797e1;
      }
    }
  }
}

.ai-cabin-extractor-button {
  background: #fff;
  border: 1px dashed #1797e1;
  color: #1797e1;
  font-weight: 500;
  border-radius: 5px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background: #f0f7ff;
    border-color: #40a9ff;
    color: #40a9ff;
    box-shadow: 0 2px 4px rgba(23, 151, 225, 0.1);
  }

  &:focus {
    background: #f0f7ff;
    border-color: #1797e1;
    color: #1797e1;
    box-shadow: 0 0 0 2px rgba(23, 151, 225, 0.2);
  }

  &:active {
    background: #e6f4ff;
    border-color: #1797e1;
    color: #1797e1;
  }

  .anticon {
    font-size: 14px;
    transition: transform 0.2s ease;
  }

  &:hover .anticon {
    transform: scale(1.1);
  }

  &.ant-btn-sm {
    padding: 4px 12px;
    height: 28px;
    font-size: 13px;

    .anticon {
      font-size: 13px;
    }
  }

  &.ant-btn-middle {
    padding: 6px 16px;
    height: 32px;
    font-size: 14px;
  }

  &.ant-btn-lg {
    padding: 8px 20px;
    height: 40px;
    font-size: 16px;

    .anticon {
      font-size: 16px;
    }
  }

  &:disabled {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
    cursor: not-allowed;

    &:hover {
      background: #f5f5f5;
      border-color: #d9d9d9;
      color: #bfbfbf;
      box-shadow: none;

      .anticon {
        transform: none;
      }
    }
  }
}

.ai-extraction-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  backdrop-filter: blur(6px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;

  .ai-extraction-content {
    background: white;
    border-radius: 16px;
    padding: 48px 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: center;
    min-width: 400px;
    max-width: 500px;
    animation: slideUp 0.4s ease-out;

    .extraction-icon {
      font-size: 48px;
      color: #1797e1;
      margin-bottom: 24px;
      animation: robotPulse 2s infinite;
    }

    .extraction-title {
      font-size: 20px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
    }

    .extraction-subtitle {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 32px;
      line-height: 1.5;
    }

    .extraction-progress {
      .ant-progress-text {
        font-size: 16px;
        font-weight: 600;
        color: #1797e1;
      }

      .ant-progress-bg {
        border-radius: 4px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes robotPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes flash {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-cabin-extractor-modal {
    .ant-modal {
      width: 90% !important;
      margin: 20px auto;
    }

    .ai-cabin-extractor-content {
      .input-section {
        .input-textarea {
          font-size: 16px; // 移动端增大字体
        }
      }

      .tips-section {
        padding: 12px;

        .tips-list {
          padding-left: 12px;

          li {
            font-size: 12px;
          }
        }
      }
    }
  }

  .ai-extraction-overlay {
    .ai-extraction-content {
      margin: 20px;
      min-width: auto;
      width: calc(100% - 40px);
      padding: 32px 24px;

      .extraction-icon {
        font-size: 40px;
        margin-bottom: 20px;
      }

      .extraction-title {
        font-size: 18px;
      }

      .extraction-subtitle {
        font-size: 13px;
        margin-bottom: 24px;
      }
    }
  }
}
