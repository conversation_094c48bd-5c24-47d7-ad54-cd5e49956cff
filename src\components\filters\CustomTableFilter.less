.custom-table-filter {
  .ant-checkbox-wrapper,
  .ant-radio-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    padding: 4px 0;
    font-size: 14px;
    line-height: 1.5715;
    
    &:hover {
      background-color: #f5f5f5;
      border-radius: 4px;
    }
  }

  .ant-checkbox,
  .ant-radio {
    margin-right: 8px;
  }

  .ant-checkbox-inner,
  .ant-radio-inner {
    width: 16px;
    height: 16px;
  }

  .ant-checkbox-checked .ant-checkbox-inner,
  .ant-radio-checked .ant-radio-inner {
    background-color: #1890ff;
    border-color: #1890ff;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
  }

  .ant-radio-checked .ant-radio-inner::after {
    background-color: #fff;
  }

  // 底部按钮样式
  .ant-btn {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
    line-height: 22px;
    border-radius: 4px;
    
    &.ant-btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;
      
      &:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
      
      &:active {
        background-color: #096dd9;
        border-color: #096dd9;
      }
    }
    
    &:not(.ant-btn-primary) {
      color: rgba(0, 0, 0, 0.85);
      border-color: #d9d9d9;
      background-color: #fff;
      
      &:hover {
        color: #40a9ff;
        border-color: #40a9ff;
        background-color: #fff;
      }
      
      &:active {
        color: #096dd9;
        border-color: #096dd9;
        background-color: #fff;
      }
    }
  }

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }

  // 选项区域滚动条
  .filter-options-container {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// 确保筛选面板在表格上方显示
.ant-table-filter-dropdown {
  .custom-table-filter {
    z-index: 1050;
  }
}
