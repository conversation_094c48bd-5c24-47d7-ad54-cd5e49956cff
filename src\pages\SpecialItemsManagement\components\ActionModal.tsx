import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { addSpecialitems, updateSpecialitems } from "../services";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  querySpecialItemsList: () => void;
}

const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  querySpecialItemsList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updateSpecialitems(values)
            : await addSpecialitems(values);
        const { data } = res;
        if (data.resultCode === 200) {
          querySpecialItemsList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t(
                  "dataManagement.specialItemsManagement.messages.updateSuccess"
                )
              : t("dataManagement.specialItemsManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t("dataManagement.specialItemsManagement.messages.updateFailed")
              : t("dataManagement.specialItemsManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.specialItemsManagement.modal.title.add")
          : t("dataManagement.specialItemsManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t("dataManagement.specialItemsManagement.modal.fields.id")}
            name="specialitemsid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.specialItemsManagement.modal.fields.specialItemsName"
          )}
          name="specialitemsname"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.specialItemsManagement.modal.validation.specialItemsNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.specialItemsManagement.modal.validation.specialItemsNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.specialItemsManagement.modal.placeholders.specialItemsName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
