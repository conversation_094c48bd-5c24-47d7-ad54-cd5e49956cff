import { store } from "@/store";
import {
  setAirlines,
  setPorts,
  setPackageTypes,
  setSpecialItems,
  setShipmentPlaces,
  setSuppliers,
  setMiscellaneousFees,
} from "@/store/slices/baseDataSlice";

import {
  getAllAirlineCompany,
  getAllPort,
  getAllPackageType,
  getAllSpecialitems,
  getAllShipmentPlace,
  getAllSupplier,
  getAllOtherfeesName,
} from "@/services/baseDataService";

//刷新Redux中的航司数据
export const refreshAirlines = async () => {
  try {
    const res = await getAllAirlineCompany();
    if (res.data?.resultCode === 200) {
      store.dispatch(setAirlines(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新航司数据失败", error);
  }
  return [];
};

//刷新Redux中的港口数据
export const refreshPorts = async () => {
  try {
    const res = await getAllPort();
    if (res.data?.resultCode === 200) {
      store.dispatch(setPorts(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新港口数据失败", error);
  }
  return [];
};

//刷新Redux中的包装类型数据
export const refreshPackageTypes = async () => {
  try {
    const res = await getAllPackageType();
    if (res.data?.resultCode === 200) {
      store.dispatch(setPackageTypes(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新包装类型数据失败", error);
  }
  return [];
};

//刷新Redux中的特殊货物数据
export const refreshSpecialItems = async () => {
  try {
    const res = await getAllSpecialitems();
    if (res.data?.resultCode === 200) {
      store.dispatch(setSpecialItems(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新特殊货物数据失败", error);
  }
  return [];
};

//刷新Redux中的发货地数据
export const refreshShipmentPlaces = async () => {
  try {
    const res = await getAllShipmentPlace();
    if (res.data?.resultCode === 200) {
      store.dispatch(setShipmentPlaces(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新发货地数据失败", error);
  }
  return [];
};

//刷新Redux中的供应商数据
export const refreshSuppliers = async () => {
  try {
    const res = await getAllSupplier();
    if (res.data?.resultCode === 200) {
      store.dispatch(setSuppliers(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新供应商数据失败", error);
  }
  return [];
};

//刷新Redux中的其他费用数据
export const refreshMiscellaneousFees = async () => {
  try {
    const res = await getAllOtherfeesName();
    if (res.data?.resultCode === 200) {
      store.dispatch(setMiscellaneousFees(res.data.data));
      return res.data.data;
    }
  } catch (error) {
    console.error("刷新其他费用数据失败", error);
  }
  return [];
};

//刷新Redux中的所有基础数据
export const refreshAllBaseData = async () => {
  await Promise.all([
    refreshAirlines(),
    refreshPorts(),
    refreshPackageTypes(),
    refreshSpecialItems(),
    refreshShipmentPlaces(),
    refreshSuppliers(),
  ]);
};

export default {
  refreshAirlines,
  refreshPorts,
  refreshPackageTypes,
  refreshSpecialItems,
  refreshShipmentPlaces,
  refreshSuppliers,
  refreshAllBaseData,
};
