{"name": "create_react_ts", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.6.1", "@fortawesome/fontawesome-free": "^6.7.2", "@react-three/drei": "^9.80.0", "@react-three/fiber": "^8.13.0", "@reduxjs/toolkit": "^2.6.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/three": "^0.176.0", "antd": "^5.24.7", "antd-style": "^3.7.1", "axios": "^1.8.3", "crypto-js": "^4.2.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "pinyin": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "three": "^0.150.1", "typescript": "^4.9.5", "uuidv4": "^6.2.13", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "npm run generate-version && craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint --ext .ts,.tsx src", "analyze": "npm run build && npx webpack-bundle-analyzer dist/static/js/*.js", "generate-version": "node scripts/generate-version.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react/no-unknown-property": ["error", {"ignore": ["args", "position", "rotation", "scale", "intensity", "color", "emissive", "emissiveIntensity", "metalness", "roughness", "transparent", "opacity", "side", "map", "attach"]}]}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0", "@types/antd": "^1.0.4", "@types/crypto-js": "^4.2.2", "@types/mockjs": "^1.0.10", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "babel-plugin-import": "^1.13.8", "craco-less": "^3.0.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "less": "^4.2.2", "less-loader": "^12.2.0", "mockjs": "^1.1.0", "msw": "^2.7.3", "prettier": "^3.5.3", "webpack-bundle-analyzer": "^4.10.2"}, "msw": {"workerDirectory": ["public"]}}