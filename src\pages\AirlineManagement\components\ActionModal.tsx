import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import {
  addAirlineCompany,
  updateAirlineCompany,
} from "@/services/baseDataService";
import { refreshAirlines } from "@/utils/refreshBaseData";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  queryAirlineList: () => void;
}
const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryAirlineList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updateAirlineCompany(values)
            : await addAirlineCompany(values);
        const { data } = res;
        if (data.resultCode === 200) {
          // 刷新Redux中的航司数据
          await refreshAirlines();
          // 刷新页面数据
          queryAirlineList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t("dataManagement.airlineManagement.messages.updateSuccess")
              : t("dataManagement.airlineManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t("dataManagement.airlineManagement.messages.updateFailed")
              : t("dataManagement.airlineManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.airlineManagement.modal.title.add")
          : t("dataManagement.airlineManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t("dataManagement.airlineManagement.modal.fields.id")}
            name="airlineid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.airlineManagement.modal.fields.cnAirlineName"
          )}
          name="cnairlinename"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.airlineManagement.modal.validation.cnAirlineNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.airlineManagement.modal.validation.cnAirlineNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.airlineManagement.modal.placeholders.cnAirlineName"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "dataManagement.airlineManagement.modal.fields.enAirlineName"
          )}
          name="enairlinename"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.airlineManagement.modal.validation.enAirlineNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.airlineManagement.modal.validation.enAirlineNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.airlineManagement.modal.placeholders.enAirlineName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
