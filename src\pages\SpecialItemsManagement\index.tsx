import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { getAllSpecialitems, delSpecialitems } from "./services";
import ActionModal from "./components/ActionModal";

interface SpecialItem {
  specialitemsid: number;
  specialitemsname: string;
}

const SpecialItemsManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [data, setData] = useState<SpecialItem[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const querySpecialItemsList = async () => {
    try {
      const res = await getAllSpecialitems();
      const { data } = res;
      if (data?.resultCode === 200) {
        setData(data.data);
      } else {
        message.error(data.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    querySpecialItemsList();
  }, []);

  const columns: TableProps<SpecialItem>["columns"] = [
    {
      title: t("dataManagement.specialItemsManagement.columns.id"),
      dataIndex: "specialitemsid",
      key: "specialitemsid",
    },
    {
      title: t(
        "dataManagement.specialItemsManagement.columns.specialItemsName"
      ),
      dataIndex: "specialitemsname",
      key: "specialitemsname",
    },
    {
      title: t("dataManagement.specialItemsManagement.columns.actions"),
      key: "action",
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip
            title={t("dataManagement.specialItemsManagement.actions.edit")}
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              className="edit-button"
              size="small"
            />
          </Tooltip>
          <Tooltip
            title={t("dataManagement.specialItemsManagement.actions.delete")}
          >
            <Popconfirm
              title={t(
                "dataManagement.specialItemsManagement.deleteConfirm.title"
              )}
              description={t(
                "dataManagement.specialItemsManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDel(record)}
              onCancel={handleCancel}
              okText={t(
                "dataManagement.specialItemsManagement.deleteConfirm.okText"
              )}
              cancelText={t(
                "dataManagement.specialItemsManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: SpecialItem) => {
    setMethod("edit");
    showModal();
    form.setFieldsValue(record);
  };

  const handleDel = async (record: SpecialItem) => {
    const res = await delSpecialitems({
      specialitemsid: record?.specialitemsid,
    });
    const { data } = res;
    if (data.resultCode === 200) {
      querySpecialItemsList();
      message.success(
        t("dataManagement.specialItemsManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {
    // 取消删除操作
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.specialItemsManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.specialItemsManagement.newSpecialItem")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="special-items-management">
      <TableCom<SpecialItem>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        querySpecialItemsList={querySpecialItemsList}
      />
    </div>
  );
};

export default SpecialItemsManagement;
