import React, {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Table, message } from "antd";
import type { TableProps, PaginationProps, TablePaginationConfig } from "antd";
import type { FilterValue, SorterResult } from "antd/es/table/interface";
import "./index.less";

// API请求参数接口
export interface ApiParams {
  current?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: string;
  [key: string]: any;
}

// API响应接口
export interface ApiResponse<T> {
  resultCode: number;
  message?: string;
  data: {
    list: T[];
    total: number;
    [key: string]: any;
  };
}

// 表格参数接口
export interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue | null>;
}

// 表格组件引用类型
export interface TableComRef {
  refresh: () => void; // 刷新表格数据
  reset: () => void; // 重置表格参数并刷新数据
  getParams: () => TableParams; // 获取当前表格参数
  setParams: (params: TableParams) => void; // 设置表格参数并刷新数据
  getData: () => any[]; // 获取当前数据
}

// 使用泛型 T，使组件支持任意数据结构
export interface TableComProps<T> {
  columns: TableProps<T>["columns"];
  data?: T[];
  pagination?: false | PaginationProps;
  rowKey?: TableProps<T>["rowKey"];
  filterRender?: () => React.ReactNode;

  // 分页相关属性
  serverPagination?: boolean; // 是否使用后端分页
  size?: TableProps<any>["size"];

  // 后端分页时需要的属性
  api?: (params: ApiParams) => Promise<ApiResponse<T>>; // API请求函数
  extraParams?: Record<string, any>; // 额外的API参数
  transformData?: (data: any) => T[]; // 数据转换函数
  onDataLoaded?: (data: T[], total: number) => void; // 数据加载完成回调
}

function TableComInner<T extends object>(
  props: TableComProps<T>,
  ref: React.ForwardedRef<TableComRef>
) {
  const {
    columns,
    data: externalData = [],
    pagination = { pageSize: 10 },
    rowKey = "key",
    filterRender = () => null,
    serverPagination = false, // 默认使用前端分页
    size = "middle",
    api,
    extraParams = {},
    transformData,
    onDataLoaded,
  } = props;

  const [internalData, setInternalData] = useState<T[]>([]);
  const [internalTotal, setInternalTotal] = useState<number>(0);
  const [internalLoading, setInternalLoading] = useState<boolean>(false);
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
      ...(typeof pagination === "object" ? pagination : {}),
    },
    filters: {},
  });
  const data = serverPagination ? internalData : externalData;
  const loading = serverPagination ? internalLoading : false;
  const total = serverPagination ? internalTotal : externalData.length;

  const fetchTableData = async (params: TableParams = {}) => {
    if (!api) return;

    setInternalLoading(true);

    try {
      // 合并当前表格参数和新参数
      const newParams = {
        sortField: params.sortField || tableParams.sortField,
        sortOrder: params.sortOrder || tableParams.sortOrder,

        pagination: {
          ...tableParams.pagination,
          ...params.pagination,
        },
        filters: {
          ...tableParams.filters,
          ...params.filters,
        },
      };
      setTableParams(newParams);

      const apiParams: ApiParams = {
        pageindex: newParams.pagination?.current || 1,
        pagesize: newParams.pagination?.pageSize || 10,
        sortField: newParams.sortField,
        sortOrder: newParams.sortOrder,
        ...extraParams, // 额外参数
      };

      if (newParams.filters) {
        const searchText = newParams.filters.searchText
          ? newParams.filters.searchText[0]
          : undefined;

        if (searchText) {
          apiParams.searchText = searchText;
        }

        Object.entries(newParams.filters)
          .filter(([key]) => key !== "searchText")
          .forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
              apiParams[key] = value;
            }
          });
      }

      // 调用API获取数据
      const response = await api(apiParams);
      const { data } = response;

      if (data.resultCode === 200) {
        let list = data.data || [];
        const total = data.totalNum || 0;

        // 如果提供了数据转换函数，则使用它转换数据
        if (transformData) {
          list = transformData(list);
        }
        setInternalData(list);
        setInternalTotal(total);

        if (onDataLoaded) {
          onDataLoaded(list, total);
        }
      } else {
        message.error(response.message || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据出错:", error);
      message.error("获取数据出错");
    } finally {
      setInternalLoading(false);
    }
  };

  useEffect(() => {
    if (serverPagination && api) {
      fetchTableData();
    }
  }, []);

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[]
  ) => {
    const newParams: TableParams = {
      pagination,
      filters,
      ...((sorter as SorterResult<T>).field
        ? {
            sortField: (sorter as SorterResult<T>).field as string,
            sortOrder: (sorter as SorterResult<T>).order as string,
          }
        : {}),
    };

    // 如果使用后端分页，则调用获取数据的函数
    if (serverPagination && api) {
      fetchTableData(newParams);
    } else {
      setTableParams(newParams);
    }
  };

  // 构建分页配置
  const paginationConfig = serverPagination
    ? {
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number) => `共 ${total} 条记录`,
        ...tableParams.pagination,
        // 外部传入的配置
        ...(typeof pagination === "object" ? pagination : {}),
        total: total || 0,
      }
    : pagination;

  useImperativeHandle(
    ref,
    () => ({
      refresh: () => {
        if (serverPagination && api) {
          fetchTableData(tableParams);
        }
      },
      reset: () => {
        const resetParams = {
          pagination: {
            current: 1,
            pageSize:
              (typeof pagination === "object" ? pagination.pageSize : 10) || 10,
          },
          filters: {},
        };

        if (serverPagination && api) {
          fetchTableData(resetParams);
        } else {
          setTableParams(resetParams);
        }
      },
      getParams: () => tableParams,
      setParams: (params: TableParams) => {
        if (serverPagination && api) {
          fetchTableData(params);
        } else {
          setTableParams(params);
        }
      },
      getData: () => data,
    }),
    [tableParams, data, serverPagination, api]
  );

  return (
    <div className="table_com">
      {filterRender()}
      <Table<T>
        className={`standard-table scroll-table ${data.length === 0 ? "empty-table" : ""}`}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={paginationConfig}
        rowKey={rowKey}
        size={size}
        onChange={handleTableChange}
        scroll={{
          y: 495,
        }}
      />
    </div>
  );
}

const TableCom = forwardRef(TableComInner) as <T extends object>(
  props: TableComProps<T> & { ref?: React.ForwardedRef<TableComRef> }
) => ReturnType<typeof React.createElement>;

export default TableCom;
