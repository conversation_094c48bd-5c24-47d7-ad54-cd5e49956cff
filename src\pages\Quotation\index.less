.quotation_container {
  height: 100%;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;

  .filter_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e6e8eb;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 14px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #1797e1, #40a9ff);
        border-radius: 2px;
      }
    }
  }

  .stats-container {
    margin-bottom: 24px;

    .stat-card {
      height: 100%;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
      transition: all 0.3s ease;
      overflow: hidden;
      border: 1px solid #eaedf0;

      .ant-card-body {
        padding: 20px;
      }

      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
      }

      .ant-statistic-title {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        .anticon {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .ant-statistic-content {
        display: flex;
        align-items: baseline;

        .ant-statistic-content-value {
          font-size: 28px;
          font-weight: 600;
          margin-right: 8px;
        }
      }

      .stat-progress {
        margin-top: 16px;

        .ant-progress-bg {
          height: 6px !important;
          border-radius: 3px;
        }
      }
    }
  }

  .tabs-container {
    .custom-tabs {
      .ant-tabs-nav {
        &::before {
          border-bottom: 1px solid #e6e8eb;
        }

        .ant-tabs-tab {
          padding: 5px 8px;
          margin: 0 4px 0 0;
          border-radius: 8px 8px 0 0;
          background-color: #f0f2f5;
          border: 1px solid #e6e8eb;
          border-bottom: none;
          transition: all 0.3s;

          &:hover {
            background-color: #e6f7ff;
          }

          &.ant-tabs-tab-active {
            background-color: #fff;
            border-top: 2px solid #1797e1;

            .ant-tabs-tab-btn {
              color: #1797e1;
              font-weight: 500;
            }
          }

          .tab-label {
            display: flex;
            align-items: center;

            .anticon {
              margin-right: 8px;
              font-size: 14px;
            }

            .tab-badge {
              margin-left: 8px;

              .ant-badge-count {
                background-color: #f5f5f5;
                color: #666;
                box-shadow: none;
                font-weight: normal;
                font-size: 12px;
                height: 20px;
                line-height: 20px;
                padding: 0 6px;
              }
            }
          }
        }
      }
    }
  }

  .table-container {
    background: white;
    border-radius: 10px;
    padding: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid #eaedf0;

    .ant-table-wrapper {
      .ant-table {
        border-radius: 8px;
        overflow: hidden;
        min-height: 50vh;

        .ant-table-thead > tr > th {
          background-color: #f5f7fa;
          color: #1f2937;
          font-weight: 500;
          border-bottom: 1px solid #e6e8eb;
          white-space: nowrap;
          overflow: visible;
          padding: 12px 8px;

          &.ant-table-cell {
            text-align: center;
          }
        }

        .ant-table-tbody > tr > td {
          padding: 10px 8px;
          text-align: center;

          &.ant-table-cell {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .scroll-table {
      &.empty-table {
        .ant-table-container {
          overflow-x: hidden !important;

          .ant-table-content {
            overflow-x: hidden !important;
          }

          .ant-table-body {
            overflow-x: hidden !important;
          }
        }
      }
      .ant-table {
        .ant-table-body,
        .ant-table-content {
          scrollbar-width: thin;
          scrollbar-color: #eaeaea transparent;
          scrollbar-gutter: stable;

          &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #eaeaea;
            border-radius: 4px;
            border: 2px solid transparent;
          }

          &::-webkit-scrollbar-thumb:hover {
            background-color: #d0d0d0;
          }
        }
      }

      .ant-empty {
        padding: 40px 0;

        .ant-empty-image {
          height: 80px;
        }

        .ant-empty-description {
          color: #6b7280;
          font-size: 15px;
        }
      }
    }
  }

  .custom-tag {
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 12px;

    &.blue {
      background-color: #e6f7ff;
      color: #1797e1;
      border: 1px solid #91d5ff;
    }

    &.green {
      background-color: #e6fffa;
      color: #10b981;
      border: 1px solid #8ddec7;
    }

    &.orange {
      background-color: #fff7e6;
      color: #fa8c16;
      border: 1px solid #ffd591;
    }
  }

  .status-tag {
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;

    .anticon {
      margin-right: 4px;
      font-size: 12px;
    }
  }

  .custom-badge {
    display: flex;
    align-items: center;

    .badge-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;

      &.success {
        background-color: #10b981;
      }

      &.default {
        background-color: #d1d5db;
      }
    }

    .badge-text {
      font-size: 14px;
      color: #4b5563;
    }
  }

  .operation-buttons {
    .ant-btn {
      border-radius: 6px;

      &.edit-button {
        color: @primary-color;

        &:hover {
          background-color: #e6f7ff;
        }
      }

      &.delete-button {
        color: #ef4444;

        &:hover {
          background-color: #fef2f2;
        }
      }
    }
  }

  .ant-pagination {
    margin-bottom: 0 !important;
    margin-top: 5px !important;
  }
}
