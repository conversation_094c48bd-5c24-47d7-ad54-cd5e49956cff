import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { addShipmentPlace, updateShipmentPlace } from "../services";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  queryShipmentPlaceList: () => void;
}

const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryShipmentPlaceList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updateShipmentPlace(values)
            : await addShipmentPlace(values);
        const { data } = res;
        if (data.resultCode === 200) {
          queryShipmentPlaceList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t(
                  "dataManagement.shipmentPlaceManagement.messages.updateSuccess"
                )
              : t("dataManagement.shipmentPlaceManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t(
                  "dataManagement.shipmentPlaceManagement.messages.updateFailed"
                )
              : t("dataManagement.shipmentPlaceManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.shipmentPlaceManagement.modal.title.add")
          : t("dataManagement.shipmentPlaceManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t("dataManagement.shipmentPlaceManagement.modal.fields.id")}
            name="placeid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.shipmentPlaceManagement.modal.fields.shipmentPlace"
          )}
          name="shipmentplace"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.shipmentPlaceRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.shipmentPlaceMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.shipmentPlaceManagement.modal.placeholders.shipmentPlace"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "dataManagement.shipmentPlaceManagement.modal.fields.enPlace"
          )}
          name="enplace"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.enPlaceRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.enPlaceMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.shipmentPlaceManagement.modal.placeholders.enPlace"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "dataManagement.shipmentPlaceManagement.modal.fields.shipmentProvince"
          )}
          name="shipmentprovince"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.shipmentProvinceRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.shipmentProvinceMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.shipmentPlaceManagement.modal.placeholders.shipmentProvince"
            )}
          />
        </Form.Item>

        <Form.Item
          label={t(
            "dataManagement.shipmentPlaceManagement.modal.fields.enProvince"
          )}
          name="enprovince"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.enProvinceRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.shipmentPlaceManagement.modal.validation.enProvinceMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.shipmentPlaceManagement.modal.placeholders.enProvince"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
