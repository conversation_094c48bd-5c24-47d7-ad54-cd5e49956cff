import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { delSupplier } from "@/services/baseDataService";
import ActionModal from "./components/ActionModal";
import useBaseData from "@/hooks/useBaseData";

const SupplierManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const { suppliers: data, loadSuppliers } = useBaseData();

  useEffect(() => {
    loadSuppliers();
  }, []);

  const columns: TableProps<any>["columns"] = [
    {
      title: t("dataManagement.supplierManagement.columns.supplierName"),
      dataIndex: "suppliername",
      key: "suppliername",
    },
    {
      title: t("dataManagement.supplierManagement.columns.actions"),
      key: "actions",
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title={t("dataManagement.supplierManagement.actions.edit")}>
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t("dataManagement.supplierManagement.deleteConfirm.title")}
            description={t(
              "dataManagement.supplierManagement.deleteConfirm.description"
            )}
            onConfirm={() => handleDelete(record)}
            okText={t("dataManagement.supplierManagement.deleteConfirm.okText")}
            cancelText={t(
              "dataManagement.supplierManagement.deleteConfirm.cancelText"
            )}
          >
            <Tooltip
              title={t("dataManagement.supplierManagement.actions.delete")}
            >
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setMethod("add");
    form.resetFields();
    setIsModalOpen(true);
  };

  const handleEdit = (record: any) => {
    setMethod("edit");
    form.setFieldsValue(record);
    setIsModalOpen(true);
  };

  const handleDelete = async (record: any) => {
    try {
      const res = await delSupplier({ supplierid: record.supplierid });
      const { data } = res;
      if (data.resultCode === 200) {
        loadSuppliers(true);
        message.success(
          t("dataManagement.supplierManagement.messages.deleteSuccess")
        );
      } else {
        message.error("删除失败");
      }
    } catch (error) {
      console.error("删除供应商失败", error);
      message.error("删除失败");
    }
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.supplierManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.supplierManagement.newSupplier")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="supplier-management">
      <TableCom<any>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        querySupplierList={() => loadSuppliers(true)}
      />
    </div>
  );
};

export default SupplierManagement;
