import React from "react";
import { Dropdown, <PERSON><PERSON>, MenuProps } from "antd";
import { GlobalOutlined } from "@ant-design/icons";

import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setLanguage, Language } from "@/store/slices/languageSlice";
import "./index.less";

const LanguageSwitcher: React.FC = () => {
  const dispatch = useAppDispatch();
  const { currentLanguage } = useAppSelector((state) => state.language);

  const handleLanguageChange = (language: Language) => {
    dispatch(setLanguage(language));
  };

  const languageOptions: MenuProps["items"] = [
    {
      key: "zh-CN",
      label: (
        <div className="language-option">
          <span className="language-flag">🇨🇳</span>
          <span className="language-name">简体中文</span>
        </div>
      ),
      onClick: () => handleLanguageChange("zh-CN"),
    },
    {
      key: "en-US",
      label: (
        <div className="language-option">
          <span className="language-flag">🇺🇸</span>
          <span className="language-name">English</span>
        </div>
      ),
      onClick: () => handleLanguageChange("en-US"),
    },
  ];

  const getCurrentLanguageDisplay = () => {
    switch (currentLanguage) {
      case "zh-CN":
        return (
          <div className="current-language">
            <span className="language-flag">🇨🇳</span>
            <span className="language-name">中文</span>
          </div>
        );
      case "en-US":
        return (
          <div className="current-language">
            <span className="language-flag">🇺🇸</span>
            <span className="language-name">EN</span>
          </div>
        );
      default:
        return (
          <div className="current-language">
            <GlobalOutlined />
          </div>
        );
    }
  };

  return (
    <Dropdown
      menu={{ items: languageOptions, selectedKeys: [currentLanguage] }}
      placement="bottomRight"
      trigger={["click"]}
    >
      <Button
        type="text"
        className="language-switcher-btn"
        icon={<GlobalOutlined />}
      >
        {getCurrentLanguageDisplay()}
      </Button>
    </Dropdown>
  );
};

export default LanguageSwitcher;
