import React from "react";
import { Typo<PERSON>, Input, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin } from "antd";
import { SendOutlined, RocketOutlined, PlusOutlined } from "@ant-design/icons";
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";
import CommandCenterBackground from "@/components/ThreeScene/CommandCenterBackground";
import MessageItem from "./MessageItem";
import { Message } from "../types";
import type { TextAreaRef } from "antd/es/input/TextArea";

const { Title, Text } = Typography;
const { TextArea } = Input;

interface ChatPanelProps {
  messages: Message[];
  inputValue: string;
  isTyping: boolean;
  initializing: boolean;
  inputRef: React.RefObject<TextAreaRef>;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  handleSendMessage: () => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  setInputValue: (value: string) => void;
  handleSuggestionClick: (suggestion: string) => void;
  handleQuotationSelect: (quotation: any) => void;
  onSaveQuotation?: (quotation: any) => void;
  savedQuotations?: any[];
  onStartNewConversation?: () => void;
}

const ChatPanel: React.FC<ChatPanelProps> = ({
  messages,
  inputValue,
  isTyping,
  initializing,
  inputRef,
  messagesEndRef,
  handleSendMessage,
  handleKeyDown,
  setInputValue,
  handleSuggestionClick,
  handleQuotationSelect,
  onSaveQuotation,
  savedQuotations,
  onStartNewConversation,
}) => {
  return (
    <div className="ai-chat-container">
      {/* 3D背景 */}
      <div className="canvas-container">
        <Canvas
          camera={{ position: [0, 0, 10], fov: 40 }}
          style={{ width: "100%", height: "100%" }}
          gl={{ antialias: true, alpha: true }}
        >
          <color attach="background" args={["#f0f7ff"]} />
          <fog attach="fog" args={["#f0f7ff", 15, 40]} />
          <CommandCenterBackground />

          <OrbitControls
            enableZoom={false}
            enablePan={false}
            enableRotate={true}
            autoRotate={true}
            autoRotateSpeed={0.5}
          />
        </Canvas>
      </div>

      <div className="ai-chat-header">
        <div className="header-content">
          <div className="panel-title">
            <RocketOutlined className="panel-icon" />
            <Title level={4}>AI智能报价助手</Title>
          </div>
          <div className="header-actions">
            <Tooltip title="重新开始对话（清空所有记录）">
              <Button
                type="default"
                icon={<PlusOutlined />}
                onClick={onStartNewConversation}
                disabled={initializing}
                className="new-conversation-button"
              >
                重新开始
              </Button>
            </Tooltip>
          </div>
        </div>
        <Text className="panel-subtitle">
          使用自然语言描述您的需求，AI将为您找到最佳报价
        </Text>
      </div>

      <div className="ai-chat-messages">
        {initializing && messages.length === 0 ? (
          <div className="visual-response-empty">
            <div className="empty-icon">
              <Spin size="large" />
            </div>
            <Title level={4}>AI助手正在初始化</Title>
            <Text type="secondary">正在连接服务，请稍候...</Text>
          </div>
        ) : (
          <div className="visual-response-empty">
            <div className="empty-icon">
              <RocketOutlined />
            </div>
            <Title level={4}>欢迎使用智能报价系统</Title>
            <Text type="secondary">
              请描述您的运输需求，AI将为您提供专业建议
            </Text>
          </div>
        )}

        {messages.map((message) => (
          <MessageItem
            key={message.id}
            message={message}
            handleSuggestionClick={handleSuggestionClick}
            handleQuotationSelect={handleQuotationSelect}
            onSaveQuotation={onSaveQuotation}
            savedQuotations={savedQuotations}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="ai-chat-input">
        <div className="command-input-container">
          <div className="input-wrapper">
            <div className="textarea-container">
              <TextArea
                ref={inputRef}
                className="command-input"
                placeholder={
                  initializing
                    ? "AI助手正在初始化，请稍候..."
                    : "描述您的运输需求，例如：我需要从上海到莫斯科的空运报价..."
                }
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isTyping || initializing}
                autoSize={{ minRows: 3, maxRows: 6 }}
                style={{ resize: "none" }}
              />
              <div className="send-button-overlay">
                <Tooltip title="发送">
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSendMessage}
                    disabled={isTyping || initializing || !inputValue.trim()}
                    className="send-button"
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPanel;
