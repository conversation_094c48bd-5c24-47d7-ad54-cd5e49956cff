.domestic-price-management {
  height: 100%;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;

  .filter_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 12px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background-color: #1797e1;
        border-radius: 2px;
      }
    }
  }

  .operation-buttons {
    .ant-btn {
      border-radius: 6px;

      &.view-button {
        color: #10b981;

        &:hover {
          background-color: #ecfdf5;
        }
      }

      &.edit-button {
        color: @primary-color;

        &:hover {
          background-color: #e6f7ff;
        }
      }

      &.delete-button {
        color: #ef4444;

        &:hover {
          background-color: #fef2f2;
        }
      }
    }
  }

  .scroll-table {
    &.empty-table {
      .ant-table-container {
        overflow-x: hidden !important;

        .ant-table-content {
          overflow-x: hidden !important;
        }

        .ant-table-body {
          overflow-x: hidden !important;
        }
      }
    }

    .ant-table {
      min-width: 50vh;
      .ant-table-body,
      .ant-table-content {
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;
        scrollbar-gutter: stable;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #eaeaea;
          border-radius: 4px;
          border: 2px solid transparent;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: #d0d0d0;
        }
      }
    }
  }

  .ant-table-wrapper {
    .ant-table {
      border-radius: 8px;
      overflow: hidden;
      min-height: 50vh;
    }

    .ant-table-thead > tr > th {
      background-color: #f5f7fa;
      color: #1f2937;
      font-weight: 500;
      border-bottom: 1px solid #e6e8eb;
      white-space: nowrap;
      overflow: visible;
      padding: 12px 8px;

      &.ant-table-cell {
        text-align: center;
      }
    }

    .ant-table-tbody > tr > td {
      padding: 10px 8px;
      text-align: center;

      &.ant-table-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ant-table-body {
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #999;
      }
    }
  }
}
