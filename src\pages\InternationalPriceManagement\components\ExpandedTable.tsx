import React from "react";
import {
  Button,
  Form,
  InputNumber,
  Popconfirm,
  Space,
  Table,
  Tooltip,
} from "antd";
import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from "@ant-design/icons";
import { InternationalPrice } from "../types";
import { useTranslation } from "react-i18next";

interface ExpandedTableProps {
  record: InternationalPrice;
  isExpandedEditing: (intervalIndex: number, parentId: number) => boolean;
  editExpandedRow: (
    intervalIndex: number,
    parentId: number,
    intervalData: any
  ) => void;
  cancelExpandedEdit: () => void;
  saveExpandedRow: (intervalIndex: number, parentId: number) => Promise<void>;
  addExpandedRow: (parentId: number) => void;
  deleteExpandedRow: (intervalIndex: number, parentId: number) => Promise<void>;
  hasEditPermission: (record?: InternationalPrice) => boolean;
  adjustIntervalPrice: (
    intervalIndex: number,
    parentId: number,
    adjustValue: number
  ) => Promise<void>;
}

const ExpandedTable: React.FC<ExpandedTableProps> = ({
  record,
  isExpandedEditing,
  editExpandedRow,
  cancelExpandedEdit,
  saveExpandedRow,
  addExpandedRow,
  deleteExpandedRow,
  hasEditPermission,
  adjustIntervalPrice,
}) => {
  const { t } = useTranslation();

  const renderEditableCell = (
    value: any,
    intervalRecord: any,
    index: number,
    fieldName: string,
    inputType: "number" | "text" = "number"
  ) => {
    const parentId = intervalRecord.parentId;
    const editable = isExpandedEditing(index, parentId);

    if (editable) {
      return (
        <Form.Item name={fieldName} style={{ margin: 0 }}>
          {inputType === "number" ? (
            <InputNumber min={0} precision={3} style={{ width: "100%" }} />
          ) : (
            <input style={{ width: "100%" }} />
          )}
        </Form.Item>
      );
    }

    return (
      <div
        className="editable-cell-value-wrap"
        onClick={() =>
          hasEditPermission(record) &&
          editExpandedRow(index, parentId, intervalRecord)
        }
      >
        {value === null ? "-" : value}
      </div>
    );
  };

  const renderDensityRange = (_: any, intervalRecord: any, index: number) => {
    const parentId = intervalRecord.parentId;
    const editable = isExpandedEditing(index, parentId);

    if (editable) {
      return (
        <div style={{ display: "flex", gap: "4px", alignItems: "center" }}>
          <Form.Item
            name="densitylvalue"
            style={{ margin: 0, flex: 1 }}
            rules={[
              {
                required: true,
                message: t(
                  "internationalPriceManagement.actionModal.placeholders.inputDensityLowerValue"
                ),
              },
            ]}
          >
            <InputNumber min={0} precision={0} style={{ width: "100%" }} />
          </Form.Item>
          <span>-</span>
          <Form.Item
            name="densityrvalue"
            style={{ margin: 0, flex: 1 }}
            rules={[
              {
                required: true,
                message: t(
                  "internationalPriceManagement.actionModal.placeholders.inputDensityUpperValue"
                ),
              },
            ]}
          >
            <InputNumber min={0} precision={0} style={{ width: "100%" }} />
          </Form.Item>
        </div>
      );
    }

    return (
      <div
        className="editable-cell-value-wrap"
        onClick={() =>
          hasEditPermission(record) &&
          editExpandedRow(index, parentId, intervalRecord)
        }
      >
        {intervalRecord.densitylvalue}-{intervalRecord.densityrvalue}
      </div>
    );
  };

  const renderQ1000PriceCell = (
    value: any,
    intervalRecord: any,
    index: number
  ) => {
    const parentId = intervalRecord.parentId;
    const editable = isExpandedEditing(index, parentId);

    const priceControls = hasEditPermission(record) ? (
      <div className="horizontal-controls-group">
        <Tooltip
          title={t("internationalPriceManagement.actions.batchPriceIncrease")}
        >
          <Button
            type="text"
            icon={<ArrowUpOutlined />}
            size="small"
            className="control-button increase-button"
            onClick={(e) => {
              e.stopPropagation();
              adjustIntervalPrice(index, parentId, 1);
            }}
          />
        </Tooltip>
        <Tooltip
          title={t("internationalPriceManagement.actions.batchPriceDecrease")}
        >
          <Button
            type="text"
            icon={<ArrowDownOutlined />}
            size="small"
            className="control-button decrease-button"
            onClick={(e) => {
              e.stopPropagation();
              adjustIntervalPrice(index, parentId, -1);
            }}
          />
        </Tooltip>
      </div>
    ) : null;

    if (editable) {
      return (
        <div className="price-cell-with-horizontal-controls">
          <div className="price-value-area">
            <Form.Item name="q1000" style={{ margin: 0 }}>
              <InputNumber min={0} precision={3} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          {priceControls}
        </div>
      );
    }

    return (
      <div className="price-cell-with-horizontal-controls">
        <div
          className="price-value-area"
          onClick={() =>
            hasEditPermission(record) &&
            editExpandedRow(index, parentId, intervalRecord)
          }
        >
          {value === null ? "-" : value}
        </div>
        {priceControls}
      </div>
    );
  };

  const renderActionCell = (_: any, intervalRecord: any, index: number) => {
    const parentId = intervalRecord.parentId;
    const editable = isExpandedEditing(index, parentId);
    console.log(1111, intervalRecord);

    if (editable) {
      return (
        <Space size="small">
          <Tooltip title={t("internationalPriceManagement.actions.save")}>
            <Button
              type="text"
              icon={<CheckOutlined />}
              onClick={() =>
                saveExpandedRow(intervalRecord?.originalIndex, parentId)
              }
              className="save-button"
              size="small"
            />
          </Tooltip>
          <Tooltip title={t("internationalPriceManagement.actions.cancel")}>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={cancelExpandedEdit}
              className="cancel-button"
              size="small"
            />
          </Tooltip>
        </Space>
      );
    }

    return (
      <Space size="small">
        <Tooltip title={t("internationalPriceManagement.actions.edit")}>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => editExpandedRow(index, parentId, intervalRecord)}
            className="edit-button"
            size="small"
            disabled={!hasEditPermission(record)}
          />
        </Tooltip>
        <Tooltip title={t("internationalPriceManagement.actions.delete")}>
          <Popconfirm
            title={t(
              "internationalPriceManagement.expandedTable.deleteConfirm.title"
            )}
            description={t(
              "internationalPriceManagement.expandedTable.deleteConfirm.description"
            )}
            onConfirm={() =>
              deleteExpandedRow(intervalRecord?.originalIndex, parentId)
            }
            okText={t(
              "internationalPriceManagement.expandedTable.deleteConfirm.okText"
            )}
            cancelText={t(
              "internationalPriceManagement.expandedTable.deleteConfirm.cancelText"
            )}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              className="delete-button"
              size="small"
              disabled={!hasEditPermission(record)}
            />
          </Popconfirm>
        </Tooltip>
      </Space>
    );
  };

  const expandedColumns = [
    {
      title: t("internationalPriceManagement.columns.densityRange"),
      dataIndex: "densityRange",
      key: "densityRange",
      width: 150,
      render: renderDensityRange,
    },
    {
      title: t("internationalPriceManagement.columns.q100Price"),
      dataIndex: "q100",
      key: "q100",
      width: 100,
      render: (value: any, intervalRecord: any, index: number) =>
        renderEditableCell(value, intervalRecord, index, "q100"),
    },
    {
      title: t("internationalPriceManagement.columns.q300Price"),
      dataIndex: "q300",
      key: "q300",
      width: 100,
      render: (value: any, intervalRecord: any, index: number) =>
        renderEditableCell(value, intervalRecord, index, "q300"),
    },
    {
      title: t("internationalPriceManagement.columns.q500Price"),
      dataIndex: "q500",
      key: "q500",
      width: 100,
      render: (value: any, intervalRecord: any, index: number) =>
        renderEditableCell(value, intervalRecord, index, "q500"),
    },
    {
      title: t("internationalPriceManagement.columns.q1000Price"),
      dataIndex: "q1000",
      key: "q1000",
      width: 120,
      render: renderQ1000PriceCell,
    },
    {
      title: t("internationalPriceManagement.columns.actions"),
      key: "action",
      width: 120,
      render: renderActionCell,
    },
  ];

  const intervalData = (record.intervalprice || []).map((interval, index) => ({
    ...interval,
    key: `${record.priceid}-${index}`,
    parentId: record.priceid,
    originalIndex: index,
  }));

  return (
    <div className="expanded-table-container">
      <div className="expanded-table-header">
        <span className="expanded-table-title">
          {t("internationalPriceManagement.expandedTable.title")}
        </span>
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={() => addExpandedRow(record.priceid)}
          disabled={!hasEditPermission(record)}
        >
          {t("internationalPriceManagement.expandedTable.addInterval")}
        </Button>
      </div>
      <Table
        columns={expandedColumns}
        dataSource={intervalData}
        pagination={false}
        size="small"
        className="expanded-inner-table"
        rowKey="key"
      />
    </div>
  );
};

export default ExpandedTable;
