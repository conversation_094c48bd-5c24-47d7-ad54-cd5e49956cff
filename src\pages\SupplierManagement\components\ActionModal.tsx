import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { addSupplier, updateSupplier } from "@/services/baseDataService";
import { refreshSuppliers } from "@/utils/refreshBaseData";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  querySupplierList: () => void;
}
const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  querySupplierList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updateSupplier(values)
            : await addSupplier(values);
        const { data } = res;
        if (data.resultCode === 200) {
          // 刷新Redux中的供应商数据
          await refreshSuppliers();
          // 刷新页面数据
          querySupplierList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t("dataManagement.supplierManagement.messages.updateSuccess")
              : t("dataManagement.supplierManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t("dataManagement.supplierManagement.messages.updateFailed")
              : t("dataManagement.supplierManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.supplierManagement.modal.title.add")
          : t("dataManagement.supplierManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t("dataManagement.supplierManagement.modal.fields.id")}
            name="supplierid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.supplierManagement.modal.fields.supplierName"
          )}
          name="suppliername"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.supplierManagement.modal.validation.supplierNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.supplierManagement.modal.validation.supplierNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.supplierManagement.modal.placeholders.supplierName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
