import { get, post } from "../../utils/request";
export const getSupplyPriceList = (data: any) => {
  return post("/getSupplyPriceByCondition", data);
};

export const addSupplyPrice = (data: any) => {
  return post("/addSupplyPrice", data);
};

export const getInternationalPriceByInquiry = (data: any) => {
  return post("/getInternationalPriceByInquiry", data);
};

export const getAllPort = () => {
  return get("/getAllPort");
};

export const getAllShipmentPlace = () => {
  return get("/getAllShipmentPlace");
};

export const getAllSpecialitems = () => {
  return get("/getAllSpecialitems");
};

export const getAllPackageType = () => {
  return get("/getAllPackageType");
};

export const getAllAirlineCompany = () => {
  return get("/getAllAirlineCompany");
};
