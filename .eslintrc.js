module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
    "prettier",
  ],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["react", "@typescript-eslint", "prettier"],
  rules: {
    "prettier/prettier": [
      "error",
      {
        trailingComma: "es5", // 统一与 Prettier 配置保持一致
      },
    ],
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/comma-dangle": "off",
    "@typescript-eslint/no-unused-expressions": [
      "error",
      {
        allowShortCircuit: true,
        allowTernary: true,
      },
    ],
    quotes: ["error", "double"], // 确保 ESLint 和 Prettier 都使用双引号
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["warn"], // 代替 `no-unused-vars`
    "react/prop-types": "off", // 将 prop-types 规则关闭
    "react/no-unknown-property": [
      "error",
      {
        ignore: [
          "args",
          "position",
          "rotation",
          "scale",
          "intensity",
          "color",
          "emissive",
          "emissiveIntensity",
          "metalness",
          "roughness",
          "transparent",
          "opacity",
          "side",
          "map",
          "attach",
          "wireframe",
        ],
      },
    ],
  },
};
