// 定义部门接口
export interface Department {
  departmentid: number;
  departname: string;
}

// 定义用户接口
export interface User {
  userid: number;
  email: string;
  password: string;
  departmentid: number;
  fullname: string;
  telephone?: string;
  createtime: string;
  recentlogintime?: string | null;
  isvalid: boolean;
  useridentity: number;
  userremark?: string;
}

// 用户身份映射
export const userIdentityMap = {
  0: { text: "外部用户", color: "gray" },
  1: { text: "询价业务员", color: "blue" },
  2: { text: "价格业务员", color: "green" },
  3: { text: "询价部门主管", color: "orange" },
  4: { text: "价格部门主管", color: "purple" },
  5: { text: "超级管理员", color: "red" },
};
