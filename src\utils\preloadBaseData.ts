import { store } from "@/store";
import {
  setAirlines,
  setPorts,
  setPackageTypes,
  setSpecialItems,
  setShipmentPlaces,
  setSuppliers,
} from "@/store/slices/baseDataSlice";
import {
  getAllAirlineCompany,
  getAllPort,
  getAllPackageType,
  getAllSpecialitems,
  getAllShipmentPlace,
  getAllSupplier,
} from "@/services/baseDataService";

export const preloadBaseData = async () => {
  const { user } = store.getState().user;
  try {
    // 加载航司数据
    const airlinesRes = await getAllAirlineCompany();
    if (airlinesRes.data?.resultCode === 200) {
      store.dispatch(setAirlines(airlinesRes.data.data));
    }

    // 加载港口数据
    const portsRes = await getAllPort();
    if (portsRes.data?.resultCode === 200) {
      store.dispatch(setPorts(portsRes.data.data));
    }

    // 加载包装类型数据
    const packageTypesRes = await getAllPackageType();
    if (packageTypesRes.data?.resultCode === 200) {
      store.dispatch(setPackageTypes(packageTypesRes.data.data));
    }

    // 加载特殊货物数据
    const specialItemsRes = await getAllSpecialitems();
    if (specialItemsRes.data?.resultCode === 200) {
      store.dispatch(setSpecialItems(specialItemsRes.data.data));
    }

    // 加载发货地数据
    const shipmentPlacesRes = await getAllShipmentPlace();
    if (shipmentPlacesRes.data?.resultCode === 200) {
      store.dispatch(setShipmentPlaces(shipmentPlacesRes.data.data));
    }

    // 加载供应商数据
    if (user?.useridentity === 2 || user?.useridentity === 4) {
      const suppliersRes = await getAllSupplier();
      if (suppliersRes.data?.resultCode === 200) {
        store.dispatch(setSuppliers(suppliersRes.data.data));
      }
    }
  } catch (error) {
    console.error("预加载基础数据失败", error);
  }
};

export default preloadBaseData;
