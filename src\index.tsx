import React from "react";
import ReactDOM from "react-dom/client";
import "./index.less";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { Provider } from "react-redux";
import { store } from "./store";
import "antd/dist/reset.css";
import "@fortawesome/fontawesome-free/css/all.css";

// 开启 MSW（仅在开发模式下）
// async function enableMocking() {
//   if (process.env.NODE_ENV === "development") {
//     const { worker } = await import("./mocks/browser");
//     await worker.start();
//     console.log("MSW started");
//   }
// }

// enableMocking().then(() => {
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
root.render(
  <Provider store={store}>
    {/* <React.StrictMode> */}
    <App />
    {/* </React.StrictMode> */}
  </Provider>
);

reportWebVitals();
// });
