import React, { ReactElement } from "react";
import { Layout, Menu } from "antd";
import { useNavigate } from "react-router-dom";
import { routerList, MenuItem } from "@/config/router";
import * as Icon from "@ant-design/icons";
import { FileOutlined } from "@ant-design/icons";
import "./index.less";
import { useAppSelector } from "@/store/hooks";

const { Sider } = Layout;

interface SidebarProps {
  onSwitch: (checked: boolean) => void;
  checked: boolean;
}

type IconType = keyof typeof Icon;
const Sidebar: React.FC<SidebarProps> = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.user);

  // 动态获取 icon
  const iconToElement = (name: IconType): ReactElement | null => {
    const IconComponent = Icon[name] as React.ComponentType<any>;
    return IconComponent ? <IconComponent /> : null;
  };

  // 菜单数据处理
  const getItems = (items: MenuItem[]): any[] => {
    if (user?.useridentity === 0) {
      // 用户身份为0的用户只能访问AI智能报价、智能报价、智能报价2.0、3D智能报价和供应价格筛选页面
      const filteredItems = items.filter(
        (item) =>
          item.key === "/ai_quotation" ||
          item.key === "/supply_price" ||
          item.key === "/smart_quotation"
      );

      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 5) {
      const filteredItems = items.filter(
        (item) =>
          item.key === "/organization_manage" || item.key === "/data_management"
      );

      return filteredItems.map((item) => {
        if (item.key === "/data_management" && item.children) {
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else if (user?.useridentity === 1) {
      const filteredItems = items.filter(
        (item) =>
          item.key === "/quotation" ||
          item.key === "/supply_price" ||
          item.key === "/domestic_price" ||
          item.key === "/international_price"
      );

      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 2) {
      const filteredItems = items.filter(
        (item) =>
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/manual_quotation" ||
          item.key === "/data_management"
      );

      return filteredItems.map((item) => {
        if (item.key === "/data_management" && item.children) {
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else if (user?.useridentity === 3) {
      const filteredItems = items.filter(
        (item) =>
          item.key === "/quotation" ||
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/supply_price"
      );

      return filteredItems.map((item) => {
        return {
          key: item.path,
          ...(item.icon && { icon: iconToElement(item.icon) }),
          label: item.label,
          ...(item.children && { children: getItems(item.children) }),
        };
      });
    } else if (user?.useridentity === 4) {
      const filteredItems = items.filter(
        (item) =>
          item.key === "/domestic_price" ||
          item.key === "/international_price" ||
          item.key === "/manual_quotation" ||
          item.key === "/data_management"
      );

      return filteredItems.map((item) => {
        if (item.key === "/data_management" && item.children) {
          const allowedChildren = [
            "/airline_management",
            "/harbor_management",
            "/package_type_management",
            "/special_items_management",
            "/shipment_place_management",
          ];

          const filteredChildren = item.children.filter((child) =>
            allowedChildren.includes(child.key)
          );

          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            children: filteredChildren.map((child) => ({
              key: child.path,
              ...(child.icon && { icon: iconToElement(child.icon) }),
              label: child.label,
            })),
          };
        } else {
          return {
            key: item.path,
            ...(item.icon && { icon: iconToElement(item.icon) }),
            label: item.label,
            ...(item.children && { children: getItems(item.children) }),
          };
        }
      });
    } else {
      return items.map((item) => ({
        key: item.path,
        ...(item.icon && { icon: iconToElement(item.icon) }),
        label: item.label,
        ...(item.children && { children: getItems(item.children) }),
      }));
    }
  };

  return (
    <Sider theme="light" className="layout_sidebar">
      <div className="demo-logo">
        <FileOutlined className="logo-icon" />
        <span className="logo-text">报价管理系统</span>
      </div>
      <Menu
        theme="light"
        mode="inline"
        defaultSelectedKeys={["/home"]}
        selectedKeys={[window?.location?.pathname]}
        onClick={({ key }) => navigate(key)}
        items={getItems(routerList)}
      />
    </Sider>
  );
};

export default Sidebar;
