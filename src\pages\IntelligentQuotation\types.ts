// 消息数据类型
export interface Message {
  id: string;
  content: string;
  sender: "ai" | "user";
  timestamp: Date;
  isLoading?: boolean;
  suggestions?: string[];
  quotationResults?: QuotationResult[];
  quotationList?: any[];
  isQuotationList?: boolean;
  isNewRound?: boolean;
  isInfoCollectionComplete?: boolean;
}

// 报价结果数据类型
export interface QuotationResult {
  id: string;
  airline: string;
  originPort: string;
  destinationPort: string;
  price: number;
  currency: string;
  transitTime: string;
  departureSchedule: string[];
  validUntil: string;
  selected?: boolean;
  recommended?: boolean;
  rating?: number;
  serviceLevel?: string;
}
