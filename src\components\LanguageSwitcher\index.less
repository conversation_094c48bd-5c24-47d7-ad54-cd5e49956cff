.language-switcher-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .current-language {
    display: flex;
    align-items: center;
    gap: 4px;

    .language-flag {
      font-size: 16px;
      line-height: 1;
    }

    .language-name {
      font-size: 12px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .language-flag {
    font-size: 16px;
    line-height: 1;
  }

  .language-name {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.ant-dropdown-menu-dark {
  .language-option {
    .language-name {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}
