import { get, post, del } from "@/utils/request";

// 获取所有国内价格
export const getDomesticPriceList = () => {
  return get("/getDomesticPriceList");
};

export const getDomesticPriceByCondition = (data: any) => {
  return post("/getDomesticPriceByCondition", data);
};

// 添加国内价格
export const addDomesticPrice = (data: any) => {
  return post("/addDomesticPrice", data);
};

// 更新国内价格
export const updateDomesticPrice = (data: any) => {
  return post("/updateDomesticPrice", data);
};

// 删除国内价格
export const delDomesticPrice = (data: any) => {
  return del("/delDomesticPrice", data);
};
