import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { getAllPackageType, delPackageType } from "./services";
import ActionModal from "./components/ActionModal";

interface PackageType {
  packagetypeid: number;
  packagename: string;
}

const PackageTypeManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [data, setData] = useState<PackageType[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const queryPackageTypeList = async () => {
    try {
      const res = await getAllPackageType();
      const { data } = res;
      if (data?.resultCode === 200) {
        setData(data.data);
      } else {
        message.error(data.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    queryPackageTypeList();
  }, []);

  const columns: TableProps<PackageType>["columns"] = [
    {
      title: t("dataManagement.packageTypeManagement.columns.id"),
      dataIndex: "packagetypeid",
      key: "packagetypeid",
    },
    {
      title: t("dataManagement.packageTypeManagement.columns.packageName"),
      dataIndex: "packagename",
      key: "packagename",
    },
    {
      title: t("dataManagement.packageTypeManagement.columns.actions"),
      key: "action",
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip
            title={t("dataManagement.packageTypeManagement.actions.edit")}
          >
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              className="edit-button"
              size="small"
            />
          </Tooltip>
          <Tooltip
            title={t("dataManagement.packageTypeManagement.actions.delete")}
          >
            <Popconfirm
              title={t(
                "dataManagement.packageTypeManagement.deleteConfirm.title"
              )}
              description={t(
                "dataManagement.packageTypeManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDel(record)}
              onCancel={handleCancel}
              okText={t(
                "dataManagement.packageTypeManagement.deleteConfirm.okText"
              )}
              cancelText={t(
                "dataManagement.packageTypeManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: PackageType) => {
    setMethod("edit");
    showModal();
    form.setFieldsValue(record);
  };

  const handleDel = async (record: PackageType) => {
    const res = await delPackageType({ packagetypeid: record?.packagetypeid });
    const { data } = res;
    if (data.resultCode === 200) {
      queryPackageTypeList();
      message.success(
        t("dataManagement.packageTypeManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {
    // 取消删除操作
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.packageTypeManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.packageTypeManagement.newPackageType")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="package-type-management">
      <TableCom<PackageType>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryPackageTypeList={queryPackageTypeList}
      />
    </div>
  );
};

export default PackageTypeManagement;
