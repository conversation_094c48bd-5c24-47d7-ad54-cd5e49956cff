import React, { useState, useEffect } from "react";
import { But<PERSON>, Tag, Divider, Badge, Row, Col, Statistic } from "antd";
import {
  SwapOutlined,
  ClockCircleOutlined,
  ScheduleOutlined,
  BoxPlotOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined,
  InfoCircleOutlined,
  DownOutlined,
  UpOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";

interface PriceCardProps {
  item: any;
  onGenerateQuotation: (item: any, selectedDate?: string) => void;
  quoteData: any;
}

const PriceCard: React.FC<PriceCardProps> = ({
  item,
  onGenerateQuotation,
  quoteData,
}) => {
  const { t } = useTranslation();
  const [extraInfoExpanded, setExtraInfoExpanded] = useState(false);
  const [selectedFlightDate, setSelectedFlightDate] = useState<string>("");

  useEffect(() => {
    if (item?.optionaldate?.length > 0) {
      // 如果当前选中的日期不在可选日期中，或者没有选中日期，则选择第一个
      if (
        !selectedFlightDate ||
        !item.optionaldate.includes(selectedFlightDate)
      ) {
        setSelectedFlightDate(item.optionaldate[0]);
      }
    }
  }, [item?.optionaldate]);
  const getCurrentTotalPrice = () => {
    if (!selectedFlightDate || !item?.datetotalprice) {
      return 0;
    }
    const price = item.datetotalprice[selectedFlightDate];
    return typeof price === "number" ? price : 0;
  };

  // 获取当前选中日期的运费单价
  const getCurrentUnitPrice = () => {
    const unit = item?.afprice?.slice(item?.afprice?.indexOf("CNY"));

    if (!selectedFlightDate || !item?.dateafprice) {
      return "-";
    }
    const price = item.dateafprice[selectedFlightDate];
    if (typeof price === "number") {
      return (
        <span className="price-value">
          <span className="price-currency">￥</span>
          <span className="price-amount">
            {price.toFixed(2)}
            {unit}
          </span>
        </span>
      );
    }
    return "-";
  };

  // 获取当前选中日期的运费单价数值
  const getCurrentAfreightPrice = () => {
    if (!selectedFlightDate || !item?.dateafprice) {
      return item?.afreightprice || 0;
    }
    const price = item.dateafprice[selectedFlightDate];
    return typeof price === "number" ? price : item?.afreightprice || 0;
  };

  // 获取当前选中日期的时效数值
  const getCurrentValidityValue = () => {
    if (!selectedFlightDate || !item?.datevalidity) {
      return item?.validity || 0;
    }
    const validity = item.datevalidity[selectedFlightDate];
    return typeof validity === "number" ? validity : item?.validity || 0;
  };

  // 获取当前选中日期的时效
  const getCurrentTransitTime = () => {
    if (!selectedFlightDate || !item?.datevalidity) {
      return "-";
    }
    const validity = item.datevalidity[selectedFlightDate];
    if (typeof validity === "number" && validity > 0) {
      return `${validity} ${validity > 1 ? "days" : "day"}`;
    }
    return "-";
  };

  const formatBoolean = (value: boolean) => {
    return value ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>
        {t("priceCard.fields.yes")}
      </Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>
        {t("priceCard.fields.no")}
      </Tag>
    );
  };

  const renderPriceTiers = () => {
    const allTiers = [
      {
        label: t("quotationEditor.priceTypes.mPrice"),
        value: item.mprice,
        color: "#1890ff",
      },
      {
        label: t("quotationEditor.priceTypes.nPrice"),
        value: item.nprice,
        color: "#52c41a",
      },
      { label: "Q45", value: item.q45price, color: "#722ed1" },
      { label: "Q100", value: item.q100price, color: "#fa8c16" },
      { label: "Q300", value: item.q300price, color: "#eb2f96" },
      { label: "Q500", value: item.q500price, color: "#f759ab" },
      { label: "Q1000", value: item.q1000price, color: "#13c2c2" },
    ];

    const validTiers = allTiers.filter((tier) => tier.value);

    return (
      <div className="price-tiers-wrapper">
        <div className="price-tiers-row single-row">
          {validTiers.map((tier, index) => (
            <div key={index} className="price-tier-item">
              <Badge color={tier.color} text={tier.label} />
              <span className="price-tier-value">
                {tier.value.toFixed(2)}{" "}
                <span className="price-tier-unit">CNY/KG</span>
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };
  const hasFeatureTags = () => {
    return (
      item.iscabin ||
      item.istransfer ||
      (Array.isArray(item.packagecharges) && item.packagecharges.length > 0) ||
      (Array.isArray(item.specialcharges) && item.specialcharges.length > 0) ||
      !item.acceptsanction
    );
  };

  // 计算是否需要显示重量补充说明（临界价格）
  const getWeightNote = () => {
    const cw = Number(item.cwprice) || 0;
    const mPrice = Number(item.mprice) || 0;
    const nPrice = Number(item.nprice) || 0;

    // 先确定计费重（CW）小于450
    if (cw < 45) {
      if (mPrice > 0 && nPrice > 0) {
        const priceRatio = mPrice / nPrice;
        if (cw < priceRatio) {
          return t("priceCard.weightNote", { weight: priceRatio.toFixed(1) });
        }
      }
    }
    return null;
  };

  //判断是否是特价
  const getIsSpecial = (cabin: any) => {
    const density = item?.goodsdensity || 0;
    const { pricechanges = [], specialprice = [] } = cabin || {};

    if (item?.cwprice <= 100) return false;

    const isInRange = (lower: number, upper: number) =>
      lower < density && density < upper;

    return (
      pricechanges.some((item: any) =>
        isInRange(item.leftdensity, item.rightdensity)
      ) ||
      specialprice.some((item: any) =>
        isInRange(item.densitylowerlimit, item.densityupperlimit)
      )
    );
  };

  return (
    <div
      className={`price-card ${!hasFeatureTags() ? "no-feature-tags" : ""}`}
      data-price-id={item.priceid}
      onClick={() => setExtraInfoExpanded(!extraInfoExpanded)}
    >
      {hasFeatureTags() && (
        <div className="feature-tags">
          {item.iscabin && (
            <Tag color="purple" className="feature-tag" icon={<StarOutlined />}>
              {t("priceCard.features.cabin")}
            </Tag>
          )}
          {item.istransfer && (
            <Tag color="orange" className="feature-tag" icon={<SwapOutlined />}>
              {t("priceCard.features.transfer")}
            </Tag>
          )}
          {Array.isArray(item.packagecharges) &&
            item.packagecharges.length > 0 && (
              <Tag
                color="cyan"
                className="feature-tag"
                icon={<BoxPlotOutlined />}
              >
                {t("priceCard.features.packaging")}
              </Tag>
            )}
          {Array.isArray(item.specialcharges) &&
            item.specialcharges.length > 0 && (
              <Tag
                color="gold"
                className="feature-tag"
                icon={<InfoCircleOutlined />}
              >
                {t("priceCard.features.specialItems")}
              </Tag>
            )}
          {!item.acceptsanction && (
            <Tag
              color="red"
              className="feature-tag"
              icon={<ExclamationCircleOutlined />}
            >
              {t("priceCard.features.acceptSanction")}
            </Tag>
          )}
        </div>
      )}

      <div className="price-card-main">
        <div className="price-card-left">
          <div className="card-row-primary">
            {/* 航司信息 */}
            <div className="airline-section">
              <i className="fas fa-plane-departure icon-plane"></i>
              <span className="icon-label">
                {item.airlinename || item.airlines}
              </span>
            </div>

            {/* 路径信息 */}
            <div className="route-section">
              <div className="route-path">
                {item?.istransfer ? (
                  <>
                    <span className="port origin-port" title={item.originport}>
                      {item.originport}
                    </span>
                    <SwapOutlined className="route-icon" />
                    <span className="port transfer-port" title={item.transfer}>
                      {item.transfer}
                    </span>
                    <SwapOutlined className="route-icon" />
                    <span
                      className="port destination-port"
                      title={item.unloadingport}
                    >
                      {item.unloadingport}
                    </span>
                  </>
                ) : (
                  <>
                    <span className="port origin-port" title={item.originport}>
                      {item.originport}
                    </span>
                    <SwapOutlined className="route-icon" />
                    <span
                      className="port destination-port"
                      title={item.unloadingport}
                    >
                      {item.unloadingport}
                    </span>
                  </>
                )}
              </div>
            </div>

            {/* 运费单价 */}
            <div className="unit-price-section">
              <div className="price-label">
                {t("priceCard.fields.unitPrice")}
              </div>
              <div className="price-value">{getCurrentUnitPrice()}</div>
            </div>
          </div>

          <div className="card-row-secondary horizontal-layout">
            {/* 班次频率 */}
            <div className="info-item-horizontal">
              <div className="info-content">
                <ScheduleOutlined className="info-icon" />
                <span className="info-label">
                  {t("priceCard.fields.flightFrequency")}
                </span>
                <span className="info-value">
                  {item?.istransfer
                    ? `${item?.originschedules || "-"}-${item?.transferschedules || "-"}`
                    : item.originschedules || "-"}
                </span>
              </div>
            </div>

            {/* 计费重 */}
            <div className="info-item-horizontal">
              <div className="info-content">
                <InfoCircleOutlined className="info-icon" />
                <span className="info-label">
                  {t("priceCard.fields.weightCharge")}
                </span>
                <span className="info-value">
                  {item.cwprice ? `${item.cwprice} kgs` : "-"}
                </span>
              </div>
            </div>

            {/* 时效 */}
            <div className="info-item-horizontal">
              <div className="info-content">
                <ClockCircleOutlined className="info-icon" />
                <span className="info-label">
                  {t("priceCard.fields.transitTime")}
                </span>
                <span className="info-value">{getCurrentTransitTime()}</span>
              </div>
            </div>
          </div>

          {/* 近期航班 */}
          <div className="flight-selection-bar">
            <div className="flight-bar-content">
              <div className="flight-bar-label">
                <ScheduleOutlined className="flight-bar-icon" />
                <span>{t("priceCard.fields.nearestFlight")}</span>
              </div>
              <div className="flight-dates-container">
                {item?.optionaldate?.map((dateItem: string) => {
                  const cabin = item?.cabinreport?.find(
                    (cabin: any) => cabin.date === dateItem
                  );
                  return (
                    <>
                      <span
                        key={dateItem}
                        className={`flight-date-tag ${
                          selectedFlightDate === dateItem ? "selected" : ""
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedFlightDate(dateItem);
                        }}
                      >
                        {dayjs(dateItem).format("MM-DD")}
                        {item?.transferdate &&
                          item?.transferdate?.[dateItem] &&
                          ` 接
                          ${dayjs(item?.transferdate?.[dateItem]).format("MM-DD")}`}
                        {cabin?.singleinquiry ? "（单询）" : ""}
                        {getIsSpecial(cabin) ? "（特价）" : ""}
                      </span>
                    </>
                  );
                }) || []}
              </div>
            </div>
          </div>
        </div>

        {/* 总价和操作按钮 */}
        <div className="price-card-right">
          <div className="total-price-section">
            <div className="total-price">
              <Statistic
                title={t("priceCard.fields.totalPrice")}
                value={getCurrentTotalPrice()}
                prefix="￥"
                precision={1}
                valueStyle={{
                  color: "#e53e3e",
                  fontWeight: 800,
                  fontSize: "22px",
                }}
              />
              {/* 重量补充说明 */}
              {getWeightNote() && (
                <div
                  className="weight-note"
                  style={{
                    fontSize: "12px",
                    color: "#e53e3e",
                    marginTop: "4px",
                    textAlign: "center",
                    fontWeight: "600",
                  }}
                >
                  {getWeightNote()}
                </div>
              )}
            </div>
            <div className="action-buttons">
              <Button
                type="primary"
                size="middle"
                className="generate-button"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onGenerateQuotation) {
                    const priceDataWithSelectedDate = {
                      ...item,
                      selectedFlightDate,
                      selectedTotalPrice: getCurrentTotalPrice(),
                      selectedAfreightPrice: getCurrentAfreightPrice(),
                      selectedValidity: getCurrentValidityValue(),
                    };
                    onGenerateQuotation(
                      priceDataWithSelectedDate,
                      selectedFlightDate
                    );
                  }
                }}
              >
                {t("priceCard.fields.generateQuote")}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {extraInfoExpanded && (
        <div className="expanded-content">
          <Divider className="card-divider" />

          {/* 基本信息 */}
          <Row gutter={[16, 12]} className="price-detail-grid">
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <CheckCircleOutlined /> {t("priceCard.fields.etdCompliant")}
                </div>
                <div className="detail-value">
                  {formatBoolean(item.iscompliant)}
                </div>
              </div>
            </Col>

            {/* 密度、保舱和尺寸限制信息 */}
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <BoxPlotOutlined /> {t("priceCard.fields.densityRange")}
                </div>
                <div className="detail-value density-value">
                  {item.mindensity || item.maxdensity
                    ? `${item.mindensity} - ${item.maxdensity} kg/m³`
                    : "-"}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <StarOutlined /> {t("priceCard.fields.canEnsureCabin")}
                </div>
                <div className="detail-value">
                  {item.iscabin ? (
                    <Tag color="purple">{t("priceCard.fields.canEnsure")}</Tag>
                  ) : (
                    <Tag color="default">
                      {t("priceCard.fields.cannotEnsure")}
                    </Tag>
                  )}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <BoxPlotOutlined /> {t("priceCard.fields.sizeLimit")}
                </div>
                <div className="detail-value">
                  <div className="size-limits">
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.length")}:
                      </span>
                      <span className="size-value">
                        {item.lengthlimit ? `${item.lengthlimit}cm` : "-"}
                      </span>
                    </div>
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.width")}:
                      </span>
                      <span className="size-value">
                        {item.widthlimit ? `${item.widthlimit}cm` : "-"}
                      </span>
                    </div>
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.height")}:
                      </span>
                      <span className="size-value">
                        {item.heightlimit ? `${item.heightlimit}cm` : "-"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          {/* 价格梯度 */}
          <div className="price-tiers-section">
            <div className="section-subtitle">
              {t("priceCard.fields.priceGradient")}
            </div>
            {renderPriceTiers()}
          </div>

          {/* 包装费用详情 */}
          {Array.isArray(item.packagecharges) &&
            item.packagecharges.length > 0 && (
              <div className="extra-charges-section">
                <div className="charges-title">
                  {t("priceCard.fields.packagingCharges")}
                </div>
                <div className="charges-list">
                  {item.packagecharges.map((charge: any, index: number) => (
                    <div key={`package-${index}`} className="charge-item">
                      <span className="charge-name">{charge.packageItem}</span>
                      <Tag color="cyan">+{charge.value}</Tag>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* 特殊物品费用详情 */}
          {Array.isArray(item.specialcharges) &&
            item.specialcharges.length > 0 && (
              <div className="extra-charges-section">
                <div className="charges-title">
                  {t("priceCard.fields.specialItemCharges")}
                </div>
                <div className="charges-list">
                  {item.specialcharges.map((charge: any, index: number) => (
                    <div key={`special-${index}`} className="charge-item">
                      <span className="charge-name">{charge.specialItem}</span>
                      <Tag color="gold">+{charge.value}</Tag>
                    </div>
                  ))}
                </div>
              </div>
            )}
        </div>
      )}
      <div className="expand-toggle">
        {extraInfoExpanded ? (
          <UpOutlined className="expand-icon" />
        ) : (
          <DownOutlined className="expand-icon" />
        )}
      </div>
    </div>
  );
};

export default PriceCard;
