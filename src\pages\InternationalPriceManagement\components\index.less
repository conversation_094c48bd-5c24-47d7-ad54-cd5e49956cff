.add-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .fixed-title-box {
    position: fixed;
    top: 75px;
    left: 0;
    right: 0;
    z-index: 99;
    background: #fff;
    padding: 10px 10px 10px 0;
    margin: 0 15px;
    height: 46px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
  }

  .content-container {
    margin-top: 43px;
    overflow-y: auto;
    background-color: #f9fafc;
    scrollbar-width: thin;
    scrollbar-color: #eaeaea transparent;
    scrollbar-gutter: stable;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #eaeaea;
      border-radius: 4px;
      border: 2px solid transparent;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #d0d0d0;
    }

    .form-container > .ant-card-body {
      height: calc(100vh - 190px);
    }
  }

  .fixed-title-box {
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 16px;
      display: flex;
      align-items: center;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 22px;
        background: linear-gradient(to bottom, #1797e1, #40a9ff);
        border-radius: 2px;
      }
    }

    .go_back {
      display: flex;
      align-items: center;
      color: #1797e1;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover {
        background-color: rgba(23, 151, 225, 0.1);
        border-color: rgba(23, 151, 225, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(23, 151, 225, 0.15);
      }

      .anticon {
        margin-right: 8px;
      }
    }
  }

  .ant-card {
    background-color: #fff;
    overflow: hidden;

    .ant-card-head {
      border-bottom: 1px solid rgba(23, 151, 225, 0.1);
      background: linear-gradient(
        to right,
        rgba(23, 151, 225, 0.05),
        rgba(64, 169, 255, 0.02)
      );
      min-height: 56px;
      padding: 0 16px;
    }

    .ant-card-body {
      height: calc(100% - 56px);
      overflow-y: auto;
      padding: 12px 16px;
      background-color: #fff;

      .form-block {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 0;
      }

      .form-card {
        border-radius: 8px;
        background-color: white;
        border: 1px solid #eaedf0;
        transition: all 0.3s ease;
        position: relative;

        .ant-card-body {
          background: #fff;
        }
      }

      .card-title {
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 8px;
        position: relative;
        padding-left: 10px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 14px;
          background: linear-gradient(to bottom, #1797e1, #40a9ff);
          border-radius: 2px;
        }
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;

        @media (min-width: 1200px) {
          grid-template-columns: repeat(5, 1fr);
        }
      }

      .input-default {
        width: 100%;
        border-radius: 4px;

        &:hover,
        &:focus {
          border-color: #1797e1;
          box-shadow: 0 0 0 1px rgba(23, 151, 225, 0.1);
        }
      }

      .ant-form-item {
        margin-bottom: 6px;

        .ant-form-item-label {
          padding-bottom: 2px;

          > label {
            color: #374151;
            font-weight: 500;
            font-size: 13px;
          }
        }

        .ant-form-item-explain {
          font-size: 12px;
          min-height: 16px;
        }

        .ant-input,
        .ant-input-number,
        .ant-select .ant-select-selector,
        .ant-picker {
          border-radius: 4px;
          border-color: #e5e7eb;
          transition: all 0.3s;
          height: 32px;
          padding: 0 8px;
          font-size: 13px;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.01);
          display: flex;
          align-items: center;

          &:hover {
            border-color: #1797e1;
            box-shadow: 0 0 0 1px rgba(23, 151, 225, 0.1);
          }

          &:focus {
            border-color: #1797e1;
            box-shadow: 0 0 0 2px rgba(23, 151, 225, 0.1);
            outline: none;
          }
        }
        .ant-input {
          line-height: 32px;
        }

        .ant-select-multiple {
          .ant-select-selector {
            height: auto !important;
            min-height: 32px !important;
            padding: 2px 5px !important;
          }

          .ant-select-selection-overflow {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
          }

          .ant-select-selection-item {
            height: 22px;
            line-height: 20px !important;
            margin-top: 2px;
            margin-bottom: 2px;
          }

          .ant-select-selection-search {
            margin-top: 2px;
            margin-bottom: 2px;
          }
        }

        .ant-input-number {
          width: 100%;

          .ant-input-number-handler-wrap {
            border-radius: 0 4px 4px 0;
          }

          .ant-input-number-input-wrap {
            width: 100%;
          }

          .ant-input-number-input {
            height: 30px;
            line-height: 30px;
            padding: 0 8px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-top: 0;
            margin-bottom: 0;
          }

          input.ant-input-number-input {
            line-height: 1;
            padding-top: 0;
            padding-bottom: 0;
          }
        }

        .ant-switch {
          background-color: #d1d5db;
          height: 18px;
          min-width: 36px;

          .ant-switch-handle {
            width: 14px;
            height: 14px;
            top: 2px;
            left: 2px;
            transition: all 0.3s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          &.ant-switch-checked {
            background: linear-gradient(90deg, #1797e1, #40a9ff);

            .ant-switch-handle {
              left: calc(100% - 18px);
            }
          }

          &:hover:not(.ant-switch-disabled) {
            background-color: #b0b7c3;

            &.ant-switch-checked {
              background: linear-gradient(90deg, #40a9ff, #1797e1);
            }
          }
        }
      }

      .sub-title {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 6px;
        position: relative;
        padding-left: 8px;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 12px;
          background: linear-gradient(to bottom, #1797e1, #40a9ff);
          border-radius: 2px;
          opacity: 0.7;
        }
      }

      .dynamic-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        background-color: #f5f7fa;
        padding: 6px 8px;
        border-radius: 4px;
        border: 1px solid #e5e7eb;
        transition: all 0.3s;

        &:hover {
          background-color: #f0f9ff;
          border-color: #d9e8ff;
        }
      }

      .row-item {
        flex: 1;
        margin-bottom: 0;
      }

      .icon-remove {
        font-size: 14px;
        color: #ff4d4f;
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          background-color: #fff2f0;
          transform: scale(1.1);
        }
      }

      .btn-dashed {
        margin-top: 4px;
        border-style: dashed;
        border-color: #1797e1;
        color: #1797e1;
        border-radius: 4px;
        height: 28px;
        transition: all 0.3s;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
          background-color: rgba(23, 151, 225, 0.05);
        }
      }

      .branch-price-section {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        > div {
          background-color: #f5f7fa;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid #e0e0e0;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
          transition: all 0.3s;

          &:hover {
            background-color: #f0f9ff;
            border-color: #d9e8ff;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
          }
        }
      }

      .ant-divider {
        margin: 8px 0 6px;

        &.ant-divider-with-text {
          &::before,
          &::after {
            border-top-color: #e5e7eb;
          }

          .ant-divider-inner-text {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            padding: 0 12px;
          }
        }
      }

      .cabin-report-header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 6px;

        .card-title {
          margin-bottom: 0;
          flex: 1;
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    height: 56px;
    border-top: 1px solid #e6e8eb;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #fff;
    padding: 0 24px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
    z-index: 100;
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 0.95);

    .footer-button {
      .ant-btn {
        height: 32px;
        border-radius: 4px;
        padding: 0 16px;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s;
        border: 1px solid #e6e8eb;

        &:not(:last-child) {
          margin-right: 12px;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        }

        &.ant-btn-primary {
          background: linear-gradient(90deg, #1797e1, #40a9ff);
          border: none;
          box-shadow: 0 1px 4px rgba(23, 151, 225, 0.2);
          color: white;

          &:hover {
            background: linear-gradient(90deg, #40a9ff, #1797e1);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(23, 151, 225, 0.25);
          }
        }
      }
    }
  }
}

.inter-price-detail-modal {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
    .ant-modal-close {
      top: 16px;
      inset-inline-end: 20px;
    }
  }

  .ant-modal-header {
    padding: 12px 20px;
    background: linear-gradient(to right, #f0f7ff, #f9fcff);
    border-bottom: 1px solid #e6f0fa;
    .ant-tag {
      margin-left: 8px;
    }
  }

  .ant-modal-body {
    padding: 8px;
    max-height: 75vh;
    overflow-y: auto;
    background-color: #f9fafc;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  .price-detail-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;

    .title-icon {
      margin-right: 8px;
      color: #1890ff;
      font-size: 18px;
    }

    .price-id-tag {
      margin-left: 12px;
      font-weight: normal;
      font-size: 12px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .price-detail-content {
    .detail-card {
      margin-bottom: 10px;
      border-radius: 6px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
      background-color: #fff;
      border: 1px solid #e6e8eb;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }

      .ant-card-body {
        padding: 12px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
        font-size: 14px;
      }
    }

    .info-item {
      margin-bottom: 6px;

      .ant-typography {
        display: block;
        margin-bottom: 3px;
        font-size: 12px;
        line-height: 1.4;
      }

      .info-value {
        font-size: 13px;
        color: #1f2937;
        font-weight: 500;
        min-height: 18px;
        word-break: break-word;
        line-height: 1.4;
      }
    }

    .json-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 4px;
        font-size: 13px;
      }
    }

    .price-tier {
      background-color: #f9fafb;
      border-radius: 4px;
      padding: 8px;
      border: 1px solid #eaedf0;
      height: 100%;

      .tier-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 6px;
        border-bottom: 1px dashed #e5e7eb;

        .ant-badge {
          font-weight: 500;
          font-size: 12px;
        }
      }

      .tier-content {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .tier-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .ant-typography {
          margin-bottom: 0;
          font-size: 12px;
        }

        .tier-value {
          font-weight: 500;
          color: #1f2937;
          font-size: 12px;
        }
      }
    }
  }
}

.cabin-report-edit-modal {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px;
  }

  .ant-modal-header {
    padding: 10px 16px;
    background: linear-gradient(to right, #f0f7ff, #f9fcff);
    border-bottom: 1px solid #e6f0fa;
  }

  .ant-modal-body {
    padding: 10px;
    max-height: 80vh;
    overflow-y: auto;
    background-color: #f9fafc;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}

.cabin-report-header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 6px;

  .card-title {
    margin-bottom: 0;
    flex: 1;
  }
}

.cabin-report-section {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0;
    position: relative;
    padding-left: 12px;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background: linear-gradient(to bottom, #1797e1, #40a9ff);
      border-radius: 2px;
    }
  }

  .ai-cabin-extractor-button {
    border-color: #1797e1;
    color: #1797e1;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
      background-color: rgba(23, 151, 225, 0.05);
    }
  }

  .cabin-price-sections {
    margin-bottom: 0;
    padding: 0 10px;

    .ant-col {
      padding: 0 8px;
    }
  }
}
.cabin-report-item {
  margin-bottom: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;

  .cabin-report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px;
    background: linear-gradient(
      to right,
      rgba(23, 151, 225, 0.05),
      rgba(64, 169, 255, 0.02)
    );
    border-bottom: 1px solid rgba(23, 151, 225, 0.1);
    border-radius: 8px 8px 0 0;

    .cabin-report-title {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      font-weight: 600;
      color: #1f2937;

      .anticon {
        color: #1797e1;
        font-size: 16px;
      }
    }
  }

  .ant-input-number {
    width: 100%;
  }

  .cabin-basic-info {
    padding: 6px 8px;
    .ant-form-item {
      margin-bottom: 0;
    }
  }

  .cabin-price-sections {
    margin-bottom: 0;
    padding: 0 8px;
  }

  .cabin-sub-section {
    margin: 0 0 4px;
    padding: 6px;
    background-color: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e5e7eb;

    .cabin-sub-title {
      font-size: 13px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 4px;
      position: relative;
      padding-left: 8px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 12px;
        background: linear-gradient(to bottom, #1797e1, #40a9ff);
        border-radius: 2px;
        opacity: 0.8;
      }
    }

    .cabin-price-row {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
      background-color: #f5f7fa;
      padding: 6px;
      border-radius: 4px;
      border: 1px solid #e5e7eb;
      transition: all 0.3s;

      &:hover {
        background-color: #f0f9ff;
        border-color: #d9e8ff;
      }

      .row-item {
        flex: 1;
        margin-bottom: 0;
      }

      .icon-remove {
        font-size: 14px;
        color: #ff4d4f;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.3s;

        &:hover {
          background-color: #fff2f0;
          transform: scale(1.1);
        }
      }

      .row-item {
        margin-bottom: 0;

        .ant-form-item-label {
          padding-bottom: 1px;

          > label {
            font-size: 12px;
            color: #6b7280;
          }
        }
      }
    }

    .btn-dashed {
      margin-top: 4px;
      height: 26px;
      font-size: 12px;
      border-color: #1797e1;
      color: #1797e1;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
        background-color: rgba(23, 151, 225, 0.05);
      }
    }
  }
}

.add-cabin-report {
  margin-top: 6px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  border: 1px dashed #1797e1;
  color: #1797e1;

  .anticon {
    font-size: 16px;
  }
}

.interval-price-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f9ff;
    border-color: #d9e8ff;
    box-shadow: 0 2px 8px rgba(23, 151, 225, 0.08);
  }

  .common-label {
    padding-bottom: 2px;
    color: #374151;
    font-weight: 500;
    font-size: 13px;
  }

  .ant-typography {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
  }

  .ant-form-item {
    margin-bottom: 8px;

    .ant-form-item-label > label {
      font-size: 13px;
      font-weight: 500;
      color: #374151;
    }
  }

  .icon-remove {
    font-size: 16px;
    color: #ff4d4f;
    cursor: pointer;
    padding: 8px;
    border-radius: 0px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: #fff2f0;
      transform: scale(1.1);
      box-shadow: 0 2px 6px rgba(255, 77, 79, 0.2);
    }
  }
}

&.edit-button {
  color: @primary-color;
  &:hover {
    color: @primary-color !important;
    background-color: #e6f7ff !important;
  }
}

&.cancel-button {
  color: #ef4444;

  &:hover {
    color: #ef4444 !important;
    background-color: #fef2f2 !important;
  }
}

&.save-button {
  color: #22c55e;

  &:hover {
    color: #22c55e !important;
    background-color: #dcfce7 !important;
  }
}

.interval-price-table {
  .ant-table-thead > tr > th {
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
    text-align: center;
    padding: 6px 8px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    text-align: center;
    padding: 6px 8px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f0f9ff;
  }

  .ant-table {
    font-size: 12px;
  }
}

.cabin-report-detail {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f9ff;
    border-color: #d9e8ff;
    box-shadow: 0 1px 4px rgba(23, 151, 225, 0.08);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .cabin-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px dashed #e5e7eb;

    .cabin-basic-info {
      display: flex;
      gap: 8px;
      font-size: 11px;
      color: #6b7280;
      flex-wrap: wrap;

      span {
        background-color: #f3f4f6;
        padding: 1px 6px;
        border-radius: 3px;
        border: 1px solid #e5e7eb;
        white-space: nowrap;
      }
    }
  }

  .price-changes,
  .special-prices {
    margin-bottom: 6px;

    .ant-typography {
      margin-bottom: 4px;
      font-size: 12px;
      color: #374151;
    }

    .price-change-list,
    .special-price-list {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .ant-tag {
        margin: 0;
        font-size: 11px;
        padding: 1px 6px;
        border-radius: 3px;
        line-height: 1.4;
      }
    }
  }

  .special-prices {
    margin-bottom: 0;
  }

  .ant-divider {
    margin: 8px 0 6px;

    &.ant-divider-with-text {
      &::before,
      &::after {
        border-top-color: #e5e7eb;
      }

      .ant-divider-inner-text {
        font-size: 13px;
        font-weight: 600;
        color: #1f2937;
        padding: 0 8px;
      }
    }
  }

  .ant-tag {
    font-size: 12px;
    padding: 1px 6px;
    border-radius: 3px;
    line-height: 1.4;
    margin: 0 2px 2px 0;
  }
}

.transfer-date {
  color: #8b5cf6;
  font-weight: 500;
  margin-left: 4px;
}

.transfer-date-inline {
  color: #8b5cf6;
  font-size: 11px;
  margin-left: 2px;
  font-weight: 500;
}
