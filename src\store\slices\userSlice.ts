import { createSlice } from "@reduxjs/toolkit";

interface UserState {
  user: {
    userid: number;
    email: string;
    password: string;
    token: string;
    fullname: string | null;
    telephone: string | null;
    createtime: string | null;
    recentlogintime: string | null;
    isvalid: boolean;
    useridentity: number;
    userremark: string | null;
    departmentid: number | null;
  } | null;
  token: string | null;
  isAuthenticated: boolean;
}

let parsedUser = null;
try {
  parsedUser = JSON.parse(localStorage.getItem("user") || "null");
} catch (e) {
  console.error("Failed to parse user from localStorage", e);
}

const initialState: UserState = {
  user: parsedUser,
  token: localStorage.getItem("token") || null,
  isAuthenticated: !!localStorage.getItem("token"),
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserInfo: (state, action) => {
      const { user, token } = action.payload;
      state.user = user;
      state.token = token;
      state.isAuthenticated = true;
      localStorage.setItem("user", JSON.stringify(user)); // 持久化存储
      localStorage.setItem("token", token);
    },
    removeUserInfo: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      localStorage.removeItem("user");
      localStorage.removeItem("token");
    },
  },
});
export const { setUserInfo, removeUserInfo } = userSlice.actions;
export default userSlice.reducer;
