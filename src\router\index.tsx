import React, { Suspense, lazy } from "react";
import { createBrowserRouter, Navigate } from "react-router-dom";
import { Spin } from "antd";
import Layout from "@/layout";
import DefaultRoute from "@/components/DefaultRoute";

// 懒加载页面组件
const Login = lazy(() => import("@/pages/Login"));
const NotFound = lazy(() => import("@/pages/NotFound"));
const Quotation = lazy(() => import("@/pages/Quotation"));
const SupplyPrice = lazy(() => import("@/pages/SupplyPrice"));
const HarborManagement = lazy(() => import("@/pages/HarborManagement"));
const AirlineManagement = lazy(() => import("@/pages/AirlineManagement"));
const OrganizationManage = lazy(() => import("@/pages/OrganizationManage"));
const PackageTypeManagement = lazy(
  () => import("@/pages/PackageTypeManagement")
);
const SpecialItemsManagement = lazy(
  () => import("@/pages/SpecialItemsManagement")
);
const ShipmentPlaceManagement = lazy(
  () => import("@/pages/ShipmentPlaceManagement")
);
const SupplierManagement = lazy(() => import("@/pages/SupplierManagement"));
const MiscellaneousFeeManagement = lazy(
  () => import("@/pages/MiscellaneousFeeManagement")
);
const DomesticPriceManagement = lazy(
  () => import("@/pages/DomesticPriceManagement")
);
const InternationalPriceManagement = lazy(
  () => import("@/pages/InternationalPriceManagement")
);
const ManualQuotation = lazy(() => import("@/pages/ManualQuotation"));
const AIQuotation = lazy(() => import("@/pages/AIQuotation"));
const IntelligentQuotation = lazy(() => import("@/pages/IntelligentQuotation"));

// 加载组件包装器
const LazyWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Suspense
    fallback={
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "50vh",
        }}
      >
        <Spin size="large" />
      </div>
    }
  >
    {children}
  </Suspense>
);

const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />, // 使用 Layout
    children: [
      {
        path: "/",
        element: <DefaultRoute />,
      },
      {
        path: "quotation",
        element: (
          <LazyWrapper>
            <Quotation />
          </LazyWrapper>
        ),
      },
      {
        path: "supply_price",
        element: (
          <LazyWrapper>
            <SupplyPrice />
          </LazyWrapper>
        ),
      },
      {
        path: "intelligenty_quotation",
        element: (
          <LazyWrapper>
            <IntelligentQuotation />
          </LazyWrapper>
        ),
      },
      {
        path: "manual_quotation",
        element: (
          <LazyWrapper>
            <ManualQuotation />
          </LazyWrapper>
        ),
      },
      {
        path: "ai_quotation",
        element: (
          <LazyWrapper>
            <AIQuotation />
          </LazyWrapper>
        ),
      },
      {
        path: "price_management",
        element: <Navigate to="domestic_price" replace />,
      },
      {
        path: "domestic_price",
        element: (
          <LazyWrapper>
            <DomesticPriceManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "international_price",
        element: (
          <LazyWrapper>
            <InternationalPriceManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "airline_management",
        element: (
          <LazyWrapper>
            <AirlineManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "harbor_management",
        element: (
          <LazyWrapper>
            <HarborManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "organization_manage",
        element: (
          <LazyWrapper>
            <OrganizationManage />
          </LazyWrapper>
        ),
      },
      {
        path: "package_type_management",
        element: (
          <LazyWrapper>
            <PackageTypeManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "special_items_management",
        element: (
          <LazyWrapper>
            <SpecialItemsManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "shipment_place_management",
        element: (
          <LazyWrapper>
            <ShipmentPlaceManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "supplier_management",
        element: (
          <LazyWrapper>
            <SupplierManagement />
          </LazyWrapper>
        ),
      },
      {
        path: "miscellaneous_fee_management",
        element: (
          <LazyWrapper>
            <MiscellaneousFeeManagement />
          </LazyWrapper>
        ),
      },
    ],
  },
  {
    path: "/login",
    element: (
      <LazyWrapper>
        <Login />
      </LazyWrapper>
    ), // 不使用 Layout
  },
  {
    path: "*",
    element: (
      <LazyWrapper>
        <NotFound />
      </LazyWrapper>
    ), // 404 页面
  },
]);

export default router;
