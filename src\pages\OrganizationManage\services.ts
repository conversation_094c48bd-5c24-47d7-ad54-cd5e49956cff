import { get, post, del } from "../../utils/request";

// 部门相关接口
export const getAllDepartment = () => {
  return get("/getAllDepartment");
};

export const addDepartment = (data: any) => {
  return post("/addDepartment", data);
};

export const updateDepartment = (data: any) => {
  return post("/updateDepartment", data);
};

export const delDepartment = (data: any) => {
  return del("/delDepartment", data);
};

// 用户相关接口
export const getUserByCondition = (data: any) => {
  return post("/getUserByCondition", data);
};

export const addUser = (data: any) => {
  return post("/addUser", data);
};

export const updateUser = (data: any) => {
  return post("/updateUser", data);
};

export const delUser = (data: any) => {
  return del("/delUser", data);
};

export const updatePassword = (data: any) => {
  return post("/updatePassword", data);
};
