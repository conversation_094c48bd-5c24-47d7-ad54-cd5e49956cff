import React from "react";
import { Modal, Form, Input } from "antd";
import { useTranslation } from "react-i18next";
import { Department } from "../types";

interface DepartmentFormModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
  editingDepartment: Department | null;
  form: any;
}

const DepartmentFormModal: React.FC<DepartmentFormModalProps> = ({
  visible,
  onCancel,
  onOk,
  editingDepartment,
  form,
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      title={
        editingDepartment
          ? t(
              "organizationManagement.departmentManagement.departmentFormModal.title.edit"
            )
          : t(
              "organizationManagement.departmentManagement.departmentFormModal.title.add"
            )
      }
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      destroyOnClose
      centered
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        {
          <Form.Item
            label={t(
              "organizationManagement.departmentManagement.departmentFormModal.fields.departmentId"
            )}
            name="departmentid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }
        <Form.Item
          label={t(
            "organizationManagement.departmentManagement.departmentFormModal.fields.departmentName"
          )}
          name="departname"
          rules={[
            {
              required: true,
              message: t(
                "organizationManagement.departmentManagement.departmentFormModal.validation.departmentNameRequired"
              ),
            },
            {
              max: 50,
              message: t(
                "organizationManagement.departmentManagement.departmentFormModal.validation.departmentNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "organizationManagement.departmentManagement.departmentFormModal.placeholders.departmentName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DepartmentFormModal;
