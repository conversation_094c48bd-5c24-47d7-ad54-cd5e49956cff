import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { getAllPort, delPort } from "./services";
import ActionModal from "./components/ActionModal";
import { refreshPorts } from "@/utils/refreshBaseData";

export interface DataType {
  portid: number;
  cnportname: string;
  enportname: string;
}

const HarborManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [data, setData] = useState<DataType[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const queryPortList = async () => {
    try {
      const res = await getAllPort();
      const { data } = res;
      if (data?.resultCode === 200) {
        setData(data.data);
      } else {
        message.error(data.message);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    queryPortList();
  }, []);

  const columns: TableProps<DataType>["columns"] = [
    {
      title: t("dataManagement.harborManagement.columns.cnPortName"),
      dataIndex: "cnportname",
      key: "cnportname",
    },
    {
      title: t("dataManagement.harborManagement.columns.enPortName"),
      dataIndex: "enportname",
      key: "enportname",
    },
    {
      title: t("dataManagement.harborManagement.columns.actions"),
      key: "action",
      render: (_, record) => (
        <Space size="middle" className="operation-buttons">
          <Tooltip title={t("dataManagement.harborManagement.actions.edit")}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              className="edit-button"
              size="small"
            />
          </Tooltip>
          <Tooltip title={t("dataManagement.harborManagement.actions.delete")}>
            <Popconfirm
              title={t("dataManagement.harborManagement.deleteConfirm.title")}
              description={t(
                "dataManagement.harborManagement.deleteConfirm.description"
              )}
              onConfirm={() => handleDel(record)}
              onCancel={handleCancel}
              okText={t("dataManagement.harborManagement.deleteConfirm.okText")}
              cancelText={t(
                "dataManagement.harborManagement.deleteConfirm.cancelText"
              )}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                className="delete-button"
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    showModal();
    setMethod("add");
    form.resetFields();
  };

  const handleEdit = (record: DataType) => {
    setMethod("edit");
    showModal();
    form.setFieldsValue(record);
  };

  const handleDel = async (record: DataType) => {
    const res = await delPort({ portid: record?.portid });
    const { data } = res;
    if (data.resultCode === 200) {
      // 刷新Redux中的港口数据
      await refreshPorts();
      // 刷新页面数据
      queryPortList();
      message.success(
        t("dataManagement.harborManagement.messages.deleteSuccess")
      );
    } else {
      message.error(data.message);
    }
  };

  const handleCancel = () => {};

  const showModal = () => {
    setIsModalOpen(true);
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.harborManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.harborManagement.newHarbor")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="harbor-management">
      <TableCom<DataType>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryPortList={queryPortList}
      />
    </div>
  );
};

export default HarborManagement;
