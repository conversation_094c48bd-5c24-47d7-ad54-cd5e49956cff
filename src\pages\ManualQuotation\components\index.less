.manual-quotation-editor-modal {
  .ant-modal-content {
    height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .ant-modal-body {
    flex: 1;
    padding: 24px 0 0 0;
    height: calc(100% - 24px);
  }

  .quotation-editor {
    height: 100%;

    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-left {
        h3 {
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .quotation-form {
      height: calc(100% - 82px);

      .ant-row {
        height: 100%;
        .ant-col {
          height: 100%;
        }
      }
      .quotation-card {
        .ant-card-head {
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;
        }

        .quotation-textarea {
          font-family: "Courier New", Courier, monospace;
          font-size: 14px;
          line-height: 1.6;
          resize: none;
        }
      }

      .quotation-info-card {
        height: 100%;

        .ant-card-head {
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;
        }

        .ant-card-body {
          padding: 16px;
          height: calc(100% - 56px);
          overflow-y: auto;
        }

        .ant-form-item {
          margin-bottom: 12px;
        }

        .ant-divider {
          margin: 12px 0;

          .ant-divider-inner-text {
            font-size: 14px;
            color: #1797e1;
            font-weight: 500;
          }
        }
      }
    }
  }
}
