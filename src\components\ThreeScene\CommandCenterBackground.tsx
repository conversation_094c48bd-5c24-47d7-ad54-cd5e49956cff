import React, { useRef } from "react";
import { useFrame } from "@react-three/fiber";
import { Group, Mesh, Vector3, MeshBasicMaterial, DoubleSide } from "three";
interface CommandCenterBackgroundProps {
  isHolographic?: boolean;
}

// 高科技控制台背景
export const HolographicGrid: React.FC = () => {
  const gridRef = useRef<Group>(null);

  useFrame(({ clock }) => {
    if (gridRef.current) {
      gridRef.current.position.y =
        Math.sin(clock.getElapsedTime() * 0.2) * 0.05;
    }
  });

  return (
    <group ref={gridRef}>
      {/* rotation 旋转；position位置 */}
      <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]}>
        {/* 40, 40: 长宽 40 单位。
         40, 40: 分段数，决定网格线数量（更多分段 => 更密的线框）。 */}
        <planeGeometry args={[40, 40, 40, 40]} />
        {/* wireframe: 只显示边框线。transparent + opacity: 设置透明度。 */}
        <meshStandardMaterial
          color="#52cafe"
          wireframe={true}
          transparent={true}
          opacity={0.2}
        />
      </mesh>
      {/* 辅助网格 - 垂直 */}
      <mesh rotation={[0, 0, 0]} position={[0, 0, -10]}>
        <planeGeometry args={[40, 10, 40, 10]} />
        <meshStandardMaterial
          color="#76c7f2"
          wireframe={true}
          transparent={true}
          opacity={0.1}
        />
      </mesh>
      {/* 辅助网格 - 水平 */}
      <mesh rotation={[0, Math.PI / 2, 0]} position={[-10, 0, 0]}>
        <planeGeometry args={[40, 10, 40, 10]} />
        <meshStandardMaterial
          color="#76c7f2"
          wireframe={true}
          transparent={true}
          opacity={0.1}
        />
      </mesh>
    </group>
  );
};

// 数据流效果
export const DataFlows: React.FC = () => {
  const flowsRef = useRef<Group>(null);

  // 创建数据流线
  const createDataFlow = (
    startX: number,
    startY: number,
    startZ: number,
    length: number,
    direction: "x" | "y" | "z",
    color: string,
    speed: number
  ) => {
    const flowRef = useRef<Mesh>(null);
    const initialPosition = new Vector3(startX, startY, startZ);

    useFrame(({ clock }) => {
      if (flowRef.current) {
        const time = clock.getElapsedTime() * speed;
        const position = initialPosition.clone();

        // 根据方向移动
        if (direction === "x") {
          position.x += (time % length) - length / 2;
        } else if (direction === "y") {
          position.y += (time % length) - length / 2;
        } else {
          position.z += (time % length) - length / 2;
        }

        flowRef.current.position.copy(position);

        const opacity = ((Math.sin(time * 2) + 1) / 2) * 0.5 + 0.2;
        if (flowRef.current.material instanceof MeshBasicMaterial) {
          flowRef.current.material.opacity = opacity;
        }
      }
    });

    return (
      <mesh ref={flowRef} position={[startX, startY, startZ]}>
        <sphereGeometry args={[0.05, 8, 8]} />
        <meshBasicMaterial color={color} transparent={true} opacity={0.5} />
      </mesh>
    );
  };

  return (
    <group ref={flowsRef}>
      {/* 创建多个数据流 */}
      {createDataFlow(-5, 0, 0, 10, "x", "#52cafe", 1)}
      {createDataFlow(5, 0, 0, 10, "x", "#1797e1", 1.5)}
      {createDataFlow(0, -2, 0, 5, "y", "#76c7f2", 0.8)}
      {createDataFlow(0, 2, 0, 5, "y", "#40a9ff", 1.2)}
      {createDataFlow(0, 0, -5, 10, "z", "#52cafe", 1.3)}
      {createDataFlow(0, 0, 5, 10, "z", "#1797e1", 0.9)}

      {/* 对角线数据流 */}
      {createDataFlow(-5, -2, -5, 15, "x", "#52cafe", 1.1)}
      {createDataFlow(5, 2, 5, 15, "x", "#1797e1", 0.7)}
    </group>
  );
};

// 全息投影效果
export const HolographicDisplay: React.FC = () => {
  const displayRef = useRef<Group>(null);

  useFrame(({ clock }) => {
    if (displayRef.current) {
      displayRef.current.rotation.y = clock.getElapsedTime() * 0.2;
    }
  });

  return (
    <group ref={displayRef} position={[0, 0, 0]}>
      {/* 中心全息球 */}
      <mesh>
        <sphereGeometry args={[0.8, 32, 32]} />
        <meshStandardMaterial
          color="#52cafe"
          transparent={true}
          opacity={0.2}
          wireframe={true}
        />
      </mesh>

      {/* 内部核心 */}
      <mesh>
        <sphereGeometry args={[0.4, 16, 16]} />
        <meshStandardMaterial
          color="#ffffff"
          emissive="#52cafe"
          emissiveIntensity={0.5}
          transparent={true}
          opacity={0.7}
        />
      </mesh>

      {/* 环绕环 */}
      <mesh rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[1.2, 1.3, 64]} />
        <meshBasicMaterial
          color="#1797e1"
          transparent={true}
          opacity={0.3}
          side={DoubleSide}
        />
      </mesh>

      {/* 第二个环绕环 */}
      <mesh rotation={[Math.PI / 3, 0, 0]}>
        <ringGeometry args={[1.5, 1.55, 64]} />
        <meshBasicMaterial
          color="#52cafe"
          transparent={true}
          opacity={0.2}
          side={DoubleSide}
        />
      </mesh>
    </group>
  );
};

export const StarBackground: React.FC = () => {
  return (
    <>
      <ambientLight intensity={0.3} />
      <directionalLight position={[10, 10, 5]} intensity={1.2} />
      <directionalLight
        position={[-10, -10, -5]}
        intensity={0.4}
        color="#40a9ff"
      />
      <spotLight
        position={[0, 10, 0]}
        angle={0.3}
        penumbra={1}
        intensity={0.8}
        color="#1797e1"
        castShadow
      />

      {Array.from({ length: 200 }).map((_, i) => {
        const x = (Math.random() - 0.5) * 40;
        const y = (Math.random() - 0.5) * 40;
        const z = (Math.random() - 0.5) * 40;
        const size = Math.random() * 0.15 + 0.05;
        return (
          <mesh key={i} position={[x, y, z]}>
            <sphereGeometry args={[size, 16, 16]} />
            <meshStandardMaterial
              color="#40a9ff"
              emissive="#40a9ff"
              emissiveIntensity={3}
            />
          </mesh>
        );
      })}

      {/* 添加光线效果 */}
      <mesh rotation={[0, 0, Math.PI / 4]} position={[0, 0, -20]}>
        <planeGeometry args={[60, 60]} />
        <meshBasicMaterial
          color="#1797e1"
          transparent
          opacity={0.03}
          side={DoubleSide}
        />
      </mesh>
    </>
  );
};

const CommandCenterBackground: React.FC<CommandCenterBackgroundProps> = ({
  isHolographic = true,
}) => {
  return (
    <>
      <StarBackground />
      <HolographicGrid />
      <DataFlows />
      {isHolographic && <HolographicDisplay />}
    </>
  );
};

export default CommandCenterBackground;
