import React, { useState } from "react";
import { But<PERSON>, <PERSON>, Divider, <PERSON><PERSON>, <PERSON>, Col, Statistic } from "antd";
import {
  SwapOutlined,
  ClockCircleOutlined,
  ScheduleOutlined,
  BoxPlotOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined,
  InfoCircleOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";

interface AIPriceCardProps {
  item: any;
  onSaveQuotation: (item: any) => void;
  isSaved?: boolean;
}

const AIPriceCard: React.FC<AIPriceCardProps> = ({
  item,
  onSaveQuotation,
  isSaved = false,
}) => {
  const { t } = useTranslation();
  const [extraInfoExpanded, setExtraInfoExpanded] = useState(false);
  const formatBoolean = (value: boolean) => {
    return value ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>
        {t("priceCard.fields.yes")}
      </Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>
        {t("priceCard.fields.no")}
      </Tag>
    );
  };

  const formatPrice = (price: string | number) => {
    if (!price) return "-";
    return (
      <div className="price-value">
        <span className="price-currency">$</span>
        <span className="price-amount">{price}</span>
      </div>
    );
  };

  // 渲染价格梯度
  const renderPriceTiers = () => {
    const allTiers = [
      {
        label: t("quotationEditor.priceTypes.mPrice"),
        value: item.mprice,
        color: "#1890ff",
      },
      {
        label: t("quotationEditor.priceTypes.nPrice"),
        value: item.nprice,
        color: "#52c41a",
      },
      { label: "Q45", value: item.q45price, color: "#722ed1" },
      { label: "Q100", value: item.q100price, color: "#fa8c16" },
      { label: "Q300", value: item.q300price, color: "#eb2f96" },
      { label: "Q500", value: item.q500price, color: "#f759ab" },
      { label: "Q1000", value: item.q1000price, color: "#13c2c2" },
    ];

    const validTiers = allTiers.filter((tier) => tier.value);

    return (
      <div className="price-tiers-wrapper">
        <div className="price-tiers-row single-row">
          {validTiers.map((tier, index) => (
            <div key={index} className="price-tier-item">
              <Badge color={tier.color} text={tier.label} />
              <span className="price-tier-value">
                {tier.value.toFixed(2)}{" "}
                <span className="price-tier-unit">USD/KG</span>
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 计算是否需要显示重量补充说明（临界价格）
  const getWeightNote = () => {
    const cw = Number(item.cwprice) || 0;
    const mPrice = Number(item.mprice) || 0;
    const nPrice = Number(item.nprice) || 0;

    if (cw < 45 && cw < mPrice && cw < nPrice) {
      return t("priceCard.weightNote", { weight: cw });
    }
    return null;
  };

  return (
    <div
      className="price-card"
      onClick={() => setExtraInfoExpanded(!extraInfoExpanded)}
    >
      <div className="price-card-tags">
        {item.iscabin && (
          <Tag color="purple" className="feature-tag" icon={<StarOutlined />}>
            {t("priceCard.features.cabin")}
          </Tag>
        )}
        {item.istransfer && (
          <Tag color="orange" className="feature-tag" icon={<SwapOutlined />}>
            {t("priceCard.features.transfer")}
          </Tag>
        )}
        {Array.isArray(item.packagecharges) &&
          item.packagecharges.length > 0 && (
            <Tag
              color="cyan"
              className="feature-tag"
              icon={<BoxPlotOutlined />}
            >
              {t("priceCard.features.packaging")}
            </Tag>
          )}
        {Array.isArray(item.specialcharges) &&
          item.specialcharges.length > 0 && (
            <Tag
              color="gold"
              className="feature-tag"
              icon={<InfoCircleOutlined />}
            >
              {t("priceCard.features.specialItems")}
            </Tag>
          )}
      </div>

      <div className="price-card-main">
        <div className="price-card-left">
          <div className="card-row-primary">
            {/* 航司信息 */}
            <div className="airline-section">
              <i className="fas fa-plane-departure icon-plane"></i>
              <span className="icon-label">
                {item.airlinename || item.airlines}
              </span>
            </div>

            {/* 路径信息 */}
            <div className="route-section">
              <div className="route-display">
                <span className="port-code origin-port">{item.originport}</span>
                <div className="route-arrow-container">
                  {item.istransfer ? (
                    <div className="transfer-route">
                      <SwapOutlined className="route-arrow transfer" />
                      <span className="transfer-port">
                        {item.transferport || "中转"}
                      </span>
                      <SwapOutlined className="route-arrow" />
                    </div>
                  ) : (
                    <SwapOutlined className="route-arrow direct" />
                  )}
                </div>
                <span className="port-code destination-port">
                  {item.unloadingport}
                </span>
              </div>
            </div>

            {/* 单价信息 */}
            <div className="price-section">
              <div className="price-label">
                {t("priceCard.fields.unitPrice")}
              </div>
              <div className="price-display">
                <span className="price-amount">
                  {formatPrice(item.afprice)}
                </span>
              </div>
            </div>
          </div>

          <div className="card-row-secondary">
            {/* 班次频率 */}
            <div className="info-item">
              <div className="info-label">
                <ScheduleOutlined className="info-icon" />
                {t("priceCard.fields.flightFrequency")}
              </div>
              <div className="info-value">
                {item?.istransfer
                  ? `${item?.originschedules || "-"}-${item?.transferschedules || "-"}`
                  : item.originschedules || "-"}
              </div>
            </div>

            {/* 计费重 */}
            <div className="info-item">
              <div className="info-label">
                <InfoCircleOutlined className="info-icon" />
                {t("priceCard.fields.weightCharge")}
              </div>
              <div className="info-value">
                {item.cwprice ? `${item.cwprice} kgs` : "-"}
              </div>
            </div>

            {/* 时效 */}
            <div className="info-item">
              <div className="info-label">
                <ClockCircleOutlined className="info-icon" />
                {t("priceCard.fields.transitTime")}
              </div>
              <div className="info-value">
                {item.validity || "-"}{" "}
                {item.validity && item.validity > 1 ? "days" : "day"}
              </div>
            </div>

            {/* 最近航班时间 */}
            <div className="info-item">
              <div className="info-label">
                <ScheduleOutlined className="info-icon" />
                {t("priceCard.fields.nearestFlight")}
              </div>
              <div className="info-value">
                {item.departuretime ? (
                  <span className="flight-date-tag">
                    {dayjs(item.departuretime).format("MM-DD")}
                  </span>
                ) : (
                  "-"
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="price-card-right">
          <div className="total-price-section">
            <div className="total-price">
              <Statistic
                title={t("priceCard.fields.totalPrice")}
                value={item.totalprice || 0}
                prefix="$"
                precision={3}
                valueStyle={{
                  color: "#e53e3e",
                  fontWeight: 800,
                  fontSize: "24px",
                }}
              />
              {getWeightNote() && (
                <div className="weight-note">{getWeightNote()}</div>
              )}
            </div>
            <div className="action-buttons">
              <Button
                type={isSaved ? "default" : "primary"}
                size="middle"
                className="generate-button"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onSaveQuotation) {
                    onSaveQuotation(item);
                  }
                }}
                style={{
                  backgroundColor: isSaved ? "#f0f0f0" : "#1890ff",
                  borderColor: isSaved ? "#d9d9d9" : "#1890ff",
                  color: isSaved ? "#666" : "white",
                }}
              >
                {isSaved ? "已保存" : "保存报价"}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {extraInfoExpanded && (
        <div className="expanded-content">
          <Divider className="card-divider" />

          {/* 基本信息 */}
          <Row gutter={[16, 12]} className="price-detail-grid">
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <CheckCircleOutlined /> {t("priceCard.fields.etdCompliant")}
                </div>
                <div className="detail-value">
                  {formatBoolean(item.iscompliant)}
                </div>
              </div>
            </Col>

            {/* 密度、保舱和尺寸限制信息 */}
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <BoxPlotOutlined /> {t("priceCard.fields.densityRange")}
                </div>
                <div className="detail-value density-value">
                  {item.mindensity || item.maxdensity
                    ? `${item.mindensity} - ${item.maxdensity} kg/m³`
                    : "-"}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <StarOutlined /> {t("priceCard.fields.canEnsureCabin")}
                </div>
                <div className="detail-value">
                  {item.iscabin ? (
                    <Tag color="purple">{t("priceCard.fields.canEnsure")}</Tag>
                  ) : (
                    <Tag color="default">
                      {t("priceCard.fields.cannotEnsure")}
                    </Tag>
                  )}
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className="detail-item">
                <div className="detail-label">
                  <BoxPlotOutlined /> {t("priceCard.fields.sizeLimit")}
                </div>
                <div className="detail-value">
                  <div className="size-limits">
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.length")}:
                      </span>
                      <span className="size-value">
                        {item.lengthlimit ? `${item.lengthlimit}cm` : "-"}
                      </span>
                    </div>
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.width")}:
                      </span>
                      <span className="size-value">
                        {item.widthlimit ? `${item.widthlimit}cm` : "-"}
                      </span>
                    </div>
                    <div className="size-item">
                      <span className="size-label">
                        {t("priceCard.fields.height")}:
                      </span>
                      <span className="size-value">
                        {item.heightlimit ? `${item.heightlimit}cm` : "-"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          {/* 价格梯度 */}
          <div className="price-tiers-section">
            <div className="section-subtitle">
              {t("priceCard.fields.priceGradient")}
            </div>
            {renderPriceTiers()}
          </div>

          {/* 包装费用详情 */}
          {Array.isArray(item.packagecharges) &&
            item.packagecharges.length > 0 && (
              <div className="extra-charges-section">
                <div className="charges-title">
                  {t("priceCard.fields.packagingCharges")}
                </div>
                <div className="charges-list">
                  {item.packagecharges.map((charge: any, index: number) => (
                    <div key={`package-${index}`} className="charge-item">
                      <span className="charge-name">{charge.packageItem}</span>
                      <Tag color="cyan">+{charge.value}</Tag>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* 特殊物品费用详情 */}
          {Array.isArray(item.specialcharges) &&
            item.specialcharges.length > 0 && (
              <div className="extra-charges-section">
                <div className="charges-title">
                  {t("priceCard.fields.specialItemCharges")}
                </div>
                <div className="charges-list">
                  {item.specialcharges.map((charge: any, index: number) => (
                    <div key={`special-${index}`} className="charge-item">
                      <span className="charge-name">{charge.specialItem}</span>
                      <Tag color="gold">+{charge.value}</Tag>
                    </div>
                  ))}
                </div>
              </div>
            )}
        </div>
      )}

      <div className="expand-toggle">
        {extraInfoExpanded ? (
          <UpOutlined className="expand-icon" />
        ) : (
          <DownOutlined className="expand-icon" />
        )}
      </div>
    </div>
  );
};

export default AIPriceCard;
