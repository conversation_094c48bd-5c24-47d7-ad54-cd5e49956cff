// 消息数据类型
export interface Message {
  id: string;
  content: string;
  sender: "ai" | "user";
  timestamp: Date;
  isLoading?: boolean;
  suggestions?: string[];
  quotationResults?: QuotationResult[];
  quotationList?: any[]; // 供应价格列表数据
  isQuotationList?: boolean; // 标记是否为报价列表消息
  isNewRound?: boolean; // 标记是否为新对话轮次的开始
}

// 报价结果数据类型
export interface QuotationResult {
  id: string;
  airline: string;
  originPort: string;
  destinationPort: string;
  price: number;
  currency: string;
  transitTime: string;
  departureSchedule: string[];
  validUntil: string;
  selected?: boolean;
  recommended?: boolean;
  rating?: number;
  serviceLevel?: string;
}
