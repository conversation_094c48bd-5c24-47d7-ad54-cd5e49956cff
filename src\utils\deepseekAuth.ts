/**
 * 设置DeepSeek API Key
 * @param apiKey DeepSeek API Key
 */
export const setDeepSeekApiKey = (apiKey: string) => {
  localStorage.setItem("deepseekApiKey", apiKey);
};

/**
 * 获取DeepSeek API Key
 * @returns DeepSeek API Key或null
 */
export const getDeepSeekApiKey = (): string | null => {
  return localStorage.getItem("deepseekApiKey");
};

/**
 * 移除DeepSeek API Key
 */
export const removeDeepSeekApiKey = () => {
  localStorage.removeItem("deepseekApiKey");
};

/**
 * 检查DeepSeek API Key是否存在
 * @returns boolean
 */
export const hasDeepSeekApiKey = (): boolean => {
  return !!localStorage.getItem("deepseekApiKey");
};

/**
 * 验证DeepSeek API Key是否有效
 * @param apiKey API Key
 */
export const validateDeepSeekApiKey = async (
  apiKey: string
): Promise<boolean> => {
  try {
    const response = await fetch(
      `${process.env.REACT_APP_SECONDARY_BASE_URL}/chat/completions`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "deepseek-chat",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1,
        }),
      }
    );

    return response.status !== 401;
  } catch (error) {
    console.error("验证DeepSeek API Key失败:", error);
    return false;
  }
};

/**
 * 设置并验证DeepSeek API Key
 * @param apiKey API Key
 */
export const setAndValidateApiKey = async (apiKey: string) => {
  const isValid = await validateDeepSeekApiKey(apiKey);
  if (isValid) {
    setDeepSeekApiKey(apiKey);
    return { success: true, message: "API Key设置成功" };
  } else {
    return { success: false, message: "API Key无效，请检查后重试" };
  }
};

/**
 * 清除DeepSeek API Key
 */
export const clearDeepSeekApiKey = () => {
  removeDeepSeekApiKey();
};
