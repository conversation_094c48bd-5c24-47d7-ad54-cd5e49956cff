import { get, post, del } from "@/utils/request";

// 获取所有包装类型
export const getAllPackageType = () => {
  return get("/getAllPackageType");
};

// 添加包装类型
export const addPackageType = (data: any) => {
  return post("/addPackageType", data);
};

// 更新包装类型
export const updatePackageType = (data: any) => {
  return post("/updatePackageType", data);
};

// 删除包装类型
export const delPackageType = (data: any) => {
  return del("/delPackageType", data);
};
