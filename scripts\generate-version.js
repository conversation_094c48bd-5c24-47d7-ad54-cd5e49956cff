const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

/**
 * 生成版本信息文件
 * 基于Git提交哈希，只有代码变更时版本才会不同
 */
function generateVersion() {
  try {
    // 获取Git信息
    const gitHash = execSync("git rev-parse HEAD", { encoding: "utf8" }).trim();
    const shortHash = execSync("git rev-parse --short HEAD", {
      encoding: "utf8",
    }).trim();
    const gitBranch = execSync("git rev-parse --abbrev-ref HEAD", {
      encoding: "utf8",
    }).trim();

    // 检查是否有未提交的更改
    let isDirty = false;
    try {
      const status = execSync("git status --porcelain", {
        encoding: "utf8",
      }).trim();
      isDirty = status.length > 0;
    } catch (error) {
      console.warn("无法检查Git状态，可能不在Git仓库中");
    }

    // 获取最后提交时间
    const lastCommitTime = execSync("git log -1 --format=%ci", {
      encoding: "utf8",
    }).trim();

    // 生成版本信息
    const versionInfo = {
      version: shortHash + (isDirty ? "-dirty" : ""),
      gitHash: gitHash,
      shortHash: shortHash,
      branch: gitBranch,
      isDirty: isDirty,
      lastCommitTime: lastCommitTime,
      buildTime: new Date().toISOString(),
      timestamp: Date.now(),
    };

    // 确保public目录存在
    const publicDir = path.join(process.cwd(), "public");
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // 写入版本文件到public目录
    const versionPath = path.join(publicDir, "version.json");
    fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 2));

    console.log("版本文件生成成功:", versionInfo.version);
    console.log("文件位置:", versionPath);

    return versionInfo;
  } catch (error) {
    console.error("生成版本文件失败:", error.message);

    // 如果Git命令失败，使用时间戳作为备用方案
    const fallbackVersion = {
      version: Date.now().toString(),
      gitHash: "unknown",
      shortHash: "unknown",
      branch: "unknown",
      isDirty: false,
      lastCommitTime: "unknown",
      buildTime: new Date().toISOString(),
      timestamp: Date.now(),
    };

    const publicDir = path.join(process.cwd(), "public");
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    const versionPath = path.join(publicDir, "version.json");
    fs.writeFileSync(versionPath, JSON.stringify(fallbackVersion, null, 2));

    console.log("使用备用版本方案:", fallbackVersion.version);
    return fallbackVersion;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateVersion();
}

module.exports = generateVersion;
