import { useEffect, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  setAirlines,
  setPorts,
  setPackageTypes,
  setSpecialItems,
  setShipmentPlaces,
  setSuppliers,
  setMiscellaneousFees,
} from "@/store/slices/baseDataSlice";

import {
  getAllAirlineCompany,
  getAllPort,
  getAllPackageType,
  getAllSpecialitems,
  getAllShipmentPlace,
  getAllSupplier,
  getAllOtherfeesName,
} from "@/services/baseDataService";

/**
 * 自定义Hook，用于获取和更新基础数据
 */
export const useBaseData = () => {
  const dispatch = useAppDispatch();
  const baseData = useAppSelector((state) => state.baseData);
  const { user } = useAppSelector((state) => state.user);

  // 加载航司数据
  const loadAirlines = async (force = false) => {
    if (force || baseData.airlines.length === 0) {
      try {
        const res = await getAllAirlineCompany();
        if (res.data?.resultCode === 200) {
          dispatch(setAirlines(res.data.data));
        }
      } catch (error) {
        console.error("加载航司数据失败", error);
      }
    }
    return baseData.airlines;
  };

  // 加载港口数据
  const loadPorts = async (force = false) => {
    if (force || baseData.ports.length === 0) {
      try {
        const res = await getAllPort();
        if (res.data?.resultCode === 200) {
          dispatch(setPorts(res.data.data));
        }
      } catch (error) {
        console.error("加载港口数据失败", error);
      }
    }
    return baseData.ports;
  };

  // 加载包装类型数据
  const loadPackageTypes = async (force = false) => {
    if (force || baseData.packageTypes.length === 0) {
      try {
        const res = await getAllPackageType();
        if (res.data?.resultCode === 200) {
          dispatch(setPackageTypes(res.data.data));
        }
      } catch (error) {
        console.error("加载包装类型数据失败", error);
      }
    }
    return baseData.packageTypes;
  };

  // 加载特殊货物数据
  const loadSpecialItems = async (force = false) => {
    if (force || baseData.specialItems.length === 0) {
      try {
        const res = await getAllSpecialitems();
        if (res.data?.resultCode === 200) {
          dispatch(setSpecialItems(res.data.data));
        }
      } catch (error) {
        console.error("加载特殊货物数据失败", error);
      }
    }
    return baseData.specialItems;
  };

  // 加载发货地数据
  const loadShipmentPlaces = async (force = false) => {
    if (force || baseData.shipmentPlaces.length === 0) {
      try {
        const res = await getAllShipmentPlace();
        if (res.data?.resultCode === 200) {
          dispatch(setShipmentPlaces(res.data.data));
        }
      } catch (error) {
        console.error("加载发货地数据失败", error);
      }
    }
    return baseData.shipmentPlaces;
  };

  // 加载供应商数据
  const loadSuppliers = async (force = false) => {
    if (user?.useridentity === 2 || user?.useridentity === 4) {
      if (force || baseData.suppliers.length === 0) {
        try {
          const res = await getAllSupplier();
          if (res.data?.resultCode === 200) {
            dispatch(setSuppliers(res.data.data));
          }
        } catch (error) {
          console.error("加载供应商数据失败", error);
        }
      }
      return baseData.suppliers;
    }
  };

  // 加载其他费用数据
  const loadMiscellaneousFees = async (force = false) => {
    if (user?.useridentity === 2 || user?.useridentity === 4) {
      if (force || baseData.miscellaneousFees.length === 0) {
        try {
          const res = await getAllOtherfeesName();
          if (res.data?.resultCode === 200) {
            dispatch(setMiscellaneousFees(res.data.data));
          }
        } catch (error) {
          console.error("加载其他费用数据失败", error);
        }
      }
      return baseData.suppliers;
    }
  };

  // 加载所有基础数据
  const loadAll = async (force = false) => {
    await Promise.all([
      loadAirlines(force),
      loadPorts(force),
      loadPackageTypes(force),
      loadSpecialItems(force),
      loadShipmentPlaces(force),
      loadSuppliers(force),
      loadMiscellaneousFees(force),
    ]);
  };

  // 使用useMemo转换为Select组件选项格式，确保在数据更新时重新计算
  const airlineOptions = useMemo(() => {
    return baseData.airlines.map((item) => ({
      label: item.enairlinename,
      value: item.enairlinename,
      key: item.airlineid,
    }));
  }, [baseData.airlines]);

  const portOptions = useMemo(() => {
    return baseData.ports.map((item) => ({
      label: item.enportname,
      value: item.enportname,
      key: item.portid,
    }));
  }, [baseData.ports]);

  const packageTypeOptions = useMemo(() => {
    return baseData.packageTypes.map((item) => ({
      label: item.packagename,
      value: item.packagename,
      key: item.packagetypeid,
    }));
  }, [baseData.packageTypes]);

  const specialItemOptions = useMemo(() => {
    return baseData.specialItems.map((item) => ({
      label: item.specialitemsname,
      value: item.specialitemsname,
      key: item.specialitemsid,
    }));
  }, [baseData.specialItems]);

  const shipmentPlaceOptions = useMemo(() => {
    return baseData.shipmentPlaces.map((item) => ({
      label: item.enplace,
      value: item.enplace,
      key: item.placeid,
    }));
  }, [baseData.shipmentPlaces]);

  const ShippingProvinceOptions = useMemo(() => {
    return baseData.shipmentPlaces.map((item) => ({
      label: item.enprovince,
      value: item.enprovince,
      key: item.placeid,
    }));
  }, [baseData.shipmentPlaces]);

  const supplierOptions = useMemo(() => {
    return baseData.suppliers.map((item) => ({
      label: item.suppliername,
      value: item.suppliername,
      key: item.supplierid,
    }));
  }, [baseData.suppliers]);

  const miscellaneousFeeOptions = useMemo(() => {
    return baseData.miscellaneousFees.map((item) => ({
      label: item.feesname,
      value: item.feesname,
      key: item.otherfeesid,
    }));
  }, [baseData.miscellaneousFees]);

  return {
    // 原始数据
    airlines: baseData.airlines,
    ports: baseData.ports,
    packageTypes: baseData.packageTypes,
    specialItems: baseData.specialItems,
    shipmentPlaces: baseData.shipmentPlaces,
    suppliers: baseData.suppliers,
    miscellaneousFees: baseData.miscellaneousFees,

    // 加载函数
    loadAirlines,
    loadPorts,
    loadPackageTypes,
    loadSpecialItems,
    loadShipmentPlaces,
    loadSuppliers,
    loadMiscellaneousFees,
    loadAll,

    // 选项格式数据
    airlineOptions,
    portOptions,
    packageTypeOptions,
    specialItemOptions,
    shipmentPlaceOptions,
    ShippingProvinceOptions,
    supplierOptions,
    miscellaneousFeeOptions,
  };
};

export default useBaseData;
