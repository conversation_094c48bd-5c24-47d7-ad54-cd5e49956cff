.header_container {
  background: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 64px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 10;
  min-width: 0;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1797e1, #40a9ff, #1797e1);
    opacity: 0.9;
  }

  .topbar {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;

    .demo-logo {
      display: flex;
      align-items: center;
      margin-right: 28px;
      position: relative;
      padding: 0px 16px;
      border-radius: 8px;
      transition: all 0.3s ease;
      flex-shrink: 0;

      &:hover {
        background: rgba(23, 151, 225, 0.03);
      }

      &::after {
        content: "";
        position: absolute;
        right: -14px;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 24px;
        background: linear-gradient(
          to bottom,
          transparent,
          rgba(0, 0, 0, 0.06),
          transparent
        );
      }

      .logo-icon {
        font-size: 16px;
        color: @primary-color;
        margin-right: 10px;
        background: linear-gradient(
          135deg,
          rgba(23, 151, 225, 0.12),
          rgba(64, 169, 255, 0.08)
        );
        padding: 8px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(23, 151, 225, 0.1);
      }

      .logo-text {
        font-size: 17px;
        font-weight: 600;
        color: #1f2937;
        background: linear-gradient(90deg, #1797e1, #40a9ff);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 0.5px;
        position: relative;
      }
    }

    .ant-menu-horizontal {
      max-width: 900px;
      border-bottom: none;
      background: transparent;
      flex: 1;
      display: flex;
      flex-wrap: nowrap;
      white-space: nowrap;
      overflow-x: visible;

      .ant-menu-item {
        padding: 0 12px;
        margin: 0 2px;
        font-weight: 500;
        color: #4b5563;
        border-radius: 6px;
        transition:
          color 0.2s,
          background-color 0.2s;
        position: relative;
        overflow: hidden;
        will-change: color, background-color;
        flex-shrink: 0;

        &:hover {
          color: @primary-color;
          background-color: rgba(23, 151, 225, 0.05);
        }

        &::after {
          display: none;
        }

        &.ant-menu-item-selected {
          color: @primary-color;
          font-weight: 600;
          background-color: rgba(23, 151, 225, 0.08);

          &::after {
            display: none;
          }

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 16px;
            background: @primary-color;
            border-radius: 0 3px 3px 0;
          }

          .anticon {
            color: @primary-color;
          }
        }

        .anticon {
          margin-right: 6px;
          font-size: 16px;
          vertical-align: -0.125em;
        }
      }

      .ant-menu-submenu {
        &::after {
          display: none;
        }

        .ant-menu-submenu-title {
          padding: 0 16px;
          font-weight: 500;
          color: #4b5563;
          transition: color 0.2s;
          will-change: color;

          &:hover {
            color: @primary-color;
          }

          .anticon {
            margin-right: 6px;
            font-size: 16px;
            vertical-align: -0.125em;
          }
        }

        &.ant-menu-submenu-selected {
          .ant-menu-submenu-title {
            color: @primary-color;
            font-weight: 600;
          }
        }

        &:hover {
          background-color: rgba(23, 151, 225, 0.05);
        }
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;

    .action-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-left: 8px;
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s;
      color: #4b5563;
      font-size: 18px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.03);
        color: @primary-color;
      }
    }
  }

  .avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 8px;
    padding: 0 8px;
    height: 40px;
    border-radius: 20px;
    transition: all 0.3s;
    cursor: pointer;
    flex-shrink: 0;

    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }

    .ant-avatar {
      background-color: @primary-color;
      box-shadow: 0 2px 6px rgba(23, 151, 225, 0.2);
    }

    p {
      margin-left: 8px;
      margin-right: 4px;
      margin-bottom: 0;
      font-size: 14px;
      font-weight: 500;
      color: #4b5563;
    }
  }
}

.layout_sidebar {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 5;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 1px;
    background: linear-gradient(
      to bottom,
      rgba(23, 151, 225, 0.05),
      rgba(64, 169, 255, 0.1),
      rgba(23, 151, 225, 0.05)
    );
  }

  .demo-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 16px 28px;
    padding: 14px;
    height: auto;
    background: linear-gradient(
      135deg,
      rgba(23, 151, 225, 0.08),
      rgba(64, 169, 255, 0.05)
    );
    border-radius: 10px;
    position: relative;
    box-shadow: 0 2px 8px rgba(23, 151, 225, 0.05);

    &::after {
      content: "";
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40%;
      height: 2px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(23, 151, 225, 0.3),
        transparent
      );
      border-radius: 2px;
    }

    .logo-icon {
      font-size: 16px;
      color: @primary-color;
      margin-right: 10px;
      background: linear-gradient(
        135deg,
        rgba(23, 151, 225, 0.12),
        rgba(64, 169, 255, 0.08)
      );
      padding: 8px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 6px rgba(23, 151, 225, 0.1);
    }

    .logo-text {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      background: linear-gradient(90deg, #1797e1, #40a9ff);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 0.5px;
    }
  }

  .ant-menu {
    background: transparent;
    border-right: none;
    padding: 0 8px;

    .ant-menu-item {
      margin: 6px 8px;
      padding: 0 16px;
      height: 44px;
      line-height: 44px;
      border-radius: 8px;
      transition:
        color 0.2s,
        background-color 0.2s;
      position: relative;
      overflow: hidden;
      will-change: color, background-color;

      &:hover {
        color: @primary-color;
        background-color: rgba(23, 151, 225, 0.05);
      }

      &.ant-menu-item-selected {
        background-color: rgba(23, 151, 225, 0.08);
        color: @primary-color;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(23, 151, 225, 0.08);

        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 20px;
          background: @primary-color;
          border-radius: 0 3px 3px 0;
        }

        .anticon {
          color: @primary-color;
        }
      }

      .anticon {
        font-size: 16px;
        vertical-align: -0.125em;
      }
    }

    .ant-menu-submenu {
      .ant-menu-submenu-title {
        margin: 4px 0;
        padding: 0 16px;
        height: 44px;
        line-height: 44px;
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          color: @primary-color;
          background-color: rgba(23, 151, 225, 0.05);
        }

        .anticon {
          font-size: 16px;
          vertical-align: -0.125em;
        }
      }

      &.ant-menu-submenu-selected {
        .ant-menu-submenu-title {
          color: @primary-color;
          font-weight: 600;
        }
      }

      &.ant-menu-submenu-open {
        .ant-menu-submenu-title {
          color: @primary-color;
          background-color: rgba(23, 151, 225, 0.05);
        }
      }
    }

    .ant-menu-sub {
      background: transparent;

      .ant-menu-item {
        padding-left: 48px !important;
        height: 40px;
        line-height: 40px;

        &::before {
          left: 32px;
          width: 4px;
          height: 4px;
          border-radius: 50%;
        }
      }
    }
  }
}

.quick-access-bar {
  display: flex;
  align-items: center;
  height: 40px;
  background: linear-gradient(to right, #f9fafc, #f5f7fa);
  border-bottom: 1px solid #e5e7eb;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
  position: relative;
  z-index: 9;

  .quick-access-title {
    font-size: 13px;
    font-weight: 500;
    color: #6b7280;
    margin-right: 16px;
    white-space: nowrap;
  }

  .quick-access-items {
    display: flex;
    align-items: center;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .quick-access-item {
      display: flex;
      align-items: center;
      padding: 0 12px;
      height: 32px;
      border-radius: 4px;
      margin-right: 8px;
      cursor: pointer;
      transition: all 0.3s;
      white-space: nowrap;

      .anticon {
        font-size: 14px;
        margin-right: 6px;
        color: #1797e1;
      }

      span {
        font-size: 13px;
        color: #4b5563;
      }

      &:hover {
        background-color: rgba(23, 151, 225, 0.05);

        span {
          color: #1797e1;
        }
      }

      &.active {
        background-color: rgba(23, 151, 225, 0.1);

        span {
          color: #1797e1;
          font-weight: 500;
        }
      }
    }
  }
}
