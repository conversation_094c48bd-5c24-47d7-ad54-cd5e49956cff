import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import { DataType } from "../index";
import { addPort, updatePort } from "../services";
import { refreshPorts } from "@/utils/refreshBaseData";

interface ActionModalProps {
  isModalOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
  method: string;
  form: FormInstance;
  queryPortList: () => void;
}

const ActionModal: React.FC<ActionModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryPortList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values: Partial<DataType>) => {
        const res =
          method === "edit" ? await updatePort(values) : await addPort(values);
        const { data } = res;
        if (data.resultCode === 200) {
          // 刷新Redux中的港口数据
          await refreshPorts();
          // 刷新页面数据
          queryPortList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t("dataManagement.harborManagement.messages.updateSuccess")
              : t("dataManagement.harborManagement.messages.addSuccess")
          );
        } else {
          message.error(
            method === "edit"
              ? t("dataManagement.harborManagement.messages.updateFailed")
              : t("dataManagement.harborManagement.messages.addFailed")
          );
        }
      })
      .catch((errorInfo: any) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.harborManagement.modal.title.add")
          : t("dataManagement.harborManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form form={form} layout="vertical" name="harbor_form">
        {
          <Form.Item
            label={t("dataManagement.harborManagement.modal.fields.id")}
            name="portid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          name="cnportname"
          label={t("dataManagement.harborManagement.modal.fields.cnPortName")}
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.harborManagement.modal.validation.cnPortNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.harborManagement.modal.validation.cnPortNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.harborManagement.modal.placeholders.cnPortName"
            )}
          />
        </Form.Item>

        <Form.Item
          name="enportname"
          label={t("dataManagement.harborManagement.modal.fields.enPortName")}
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.harborManagement.modal.validation.enPortNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.harborManagement.modal.validation.enPortNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.harborManagement.modal.placeholders.enPortName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
