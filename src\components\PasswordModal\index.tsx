import React, { useState } from "react";
import { Modal, Form, Input, Button, message, Spin, Row, Col } from "antd";
import { LockOutlined, MailOutlined, KeyOutlined } from "@ant-design/icons";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { removeUserInfo } from "@/store/slices/userSlice";
import { useNavigate } from "react-router-dom";
import "./index.less";
import { SHA256 } from "crypto-js";
import { resetPassword, getCode } from "./services";

interface PasswordModalProps {
  visible: boolean;
  onClose: () => void;
}

const PasswordModal: React.FC<PasswordModalProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const { user } = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 发送验证码
  const handleSendVerificationCode = async () => {
    if (countdown > 0 || sendingCode) return;

    try {
      setSendingCode(true);
      const response = await getCode({ email: user?.email as string });

      if (response.data?.resultCode === 200) {
        message.success("验证码发送成功");
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.data?.message || "验证码发送失败");
      }
    } catch (error) {
      console.error("发送验证码错误:", error);
      message.error("验证码发送失败，请重试");
    } finally {
      setSendingCode(false);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (values.newPassword !== values.confirmPassword) {
        message.error("两次输入的新密码不一致");
        setLoading(false);
        return;
      }

      const resetData = {
        email: user?.email,
        verifycode: values.verificationCode,
        password: SHA256(values.newPassword).toString(),
        newpassword: SHA256(values.newPassword).toString(),
      };
      const response = await resetPassword(resetData);

      if (response.data?.resultCode === 200) {
        dispatch(removeUserInfo());
        localStorage.removeItem("token");
        localStorage.removeItem("user");

        message.success("密码修改成功，请重新登录");
        form.resetFields();
        onClose();

        setTimeout(() => {
          navigate("/login");
        }, 500);
      } else {
        message.error(response.data?.message || "密码修改失败，请重试");
      }
    } catch (error) {
      console.error("表单验证或提交出错:", error);
      message.error("提交失败，请检查表单填写是否正确");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="修改密码"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      destroyOnClose
      className="password-modal"
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical" className="password-form">
          <Form.Item label="邮箱" name="email" initialValue={user?.email}>
            <Input
              prefix={<MailOutlined />}
              placeholder="邮箱地址"
              disabled
              value={user?.email}
            />
          </Form.Item>

          <Form.Item
            label="验证码"
            name="verificationCode"
            rules={[
              { required: true, message: "请输入验证码" },
              { len: 6, message: "验证码长度为6位" },
            ]}
          >
            <Row gutter={8}>
              <Col span={16}>
                <Input
                  prefix={<KeyOutlined />}
                  placeholder="请输入6位验证码"
                  maxLength={6}
                />
              </Col>
              <Col span={8}>
                <Button
                  onClick={handleSendVerificationCode}
                  loading={sendingCode}
                  disabled={countdown > 0}
                  block
                >
                  {countdown > 0 ? `${countdown}s` : "发送验证码"}
                </Button>
              </Col>
            </Row>
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: "请输入新密码" },
              // { min: 6, message: "密码长度不能少于6个字符" },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入新密码"
            />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            rules={[
              { required: true, message: "请确认新密码" },
              // { min: 6, message: "密码长度不能少于6个字符" },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
            />
          </Form.Item>

          <div className="form-actions">
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit}>
              确认修改
            </Button>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default PasswordModal;
