import { Layout } from "antd";
import { Outlet, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import Sidebar from "./components/Sidebar";
import Topbar from "./components/Topbar";
import QuickAccess from "./components/QuickAccess";
import { RouterAuth } from "@/router/routerAuth";
import { routerList } from "@/config/router";
import "./index.less";

const { Content } = Layout;

const AppLayout: React.FC = () => {
  const [menuPosition, setMenuPosition] = useState<"side" | "top">("top");
  const [checked, setChecked] = useState(true);
  const [showQuickAccess, setShowQuickAccess] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // 获取基础数据菜单及其子菜单
    const dataManagementMenu = routerList.find(
      (item) => item.key === "/data_management"
    );
    const dataSubMenuPaths =
      dataManagementMenu?.children?.map((item) => item.path) || [];

    const priceManagementMenu = routerList.find(
      (item) => item.key === "/price_management"
    );
    const priceSubMenuPaths =
      priceManagementMenu?.children?.map((item) => item.path) || [];

    const allSubMenuPaths = [...dataSubMenuPaths, ...priceSubMenuPaths];

    const shouldShowQuickAccess = allSubMenuPaths.some(
      (path) =>
        location.pathname === path || location.pathname.startsWith(path + "/")
    );

    setShowQuickAccess(shouldShowQuickAccess && menuPosition === "top");
  }, [location.pathname, menuPosition]);

  const handleSwitch = (checked: boolean) => {
    setMenuPosition(checked ? "side" : "top");
    setChecked(menuPosition === "top");
  };

  return (
    <RouterAuth>
      <Layout className="layout_container">
        {menuPosition === "side" && (
          <Sidebar onSwitch={handleSwitch} checked={checked} />
        )}
        <Layout>
          {menuPosition === "top" && <Topbar />}
          {menuPosition === "top" && showQuickAccess && <QuickAccess />}
          <Layout>
            <Content>
              <Outlet />
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </RouterAuth>
  );
};

export default AppLayout;
