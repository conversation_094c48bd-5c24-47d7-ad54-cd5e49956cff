.organization-manage-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-card-body {
      padding: 0 24px;
    }

    .page-header {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;

      .header-icon {
        margin-right: 8px;
        font-size: 20px;
        color: #1890ff;
      }
    }

    .main-tabs {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .ant-tabs-nav {
        margin-bottom: 16px;
      }

      .ant-tabs-content-holder {
        flex: 1;
        overflow: hidden;
      }

      .ant-tabs-content {
        height: 100%;
      }

      .ant-tabs-tabpane {
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .department-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .toolbar {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      flex-shrink: 0;
    }

    .department-table {
      flex: 1;
      overflow: auto;

      .ant-table-wrapper {
        height: 100%;
      }

      .ant-table-thead > tr > th {
        background-color: #f5f7fa;
        font-weight: 600;
      }

      .ant-table-body {
        overflow-y: auto !important;
      }

      .operation-buttons {
        .ant-btn {
          border-radius: 6px;

          &.edit-button {
            color: @primary-color;

            &:hover {
              background-color: #e6f7ff;
            }
          }

          &.delete-button {
            color: #ef4444;

            &:hover {
              background-color: #fef2f2;
            }
          }
        }
      }
    }
  }

  .user-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .ant-row {
      height: 100%;
      flex: 1;
    }

    .ant-col {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .department-tree-card {
      height: 100%;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        padding: 0 16px;
        flex-shrink: 0;

        .ant-card-head-title {
          padding: 12px 0;
          font-size: 15px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .ant-card-body {
        padding: 16px;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      .tree-toolbar {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        flex-shrink: 0;
      }

      .department-tree {
        flex: 1;
        overflow: auto;

        .ant-tree-treenode {
          padding: 4px 0;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .ant-tree-node-selected {
          background-color: #e6f7ff !important;
        }
      }
    }

    .user-list-container {
      background-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        flex-shrink: 0;

        .left {
          display: flex;
          align-items: center;
        }

        .right {
          display: flex;
          align-items: center;
        }
      }

      .user-table {
        flex: 1;
        overflow: hidden;

        .ant-table-wrapper {
          height: 100%;
        }

        .ant-table {
          height: 100%;
        }

        .ant-table-container {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .ant-table-body {
          flex: 1;
          overflow-y: auto !important;
        }

        .ant-table-thead > tr > th {
          background-color: #f5f7fa;
          font-weight: 600;
        }

        .ant-table-row {
          &:hover {
            .ant-table-cell {
              background-color: #f0f7ff;
            }
          }
        }

        .ant-table-row-selected > td {
          background-color: #e6f7ff !important;
        }

        .operation-buttons {
          .ant-btn {
            border-radius: 6px;

            &.edit-button {
              color: @primary-color;

              &:hover {
                background-color: #e6f7ff;
              }
            }

            &.delete-button {
              color: #ef4444;

              &:hover {
                background-color: #fef2f2;
              }
            }
          }
        }
      }
    }
  }

  .ant-modal {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
    }

    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
    }
  }

  .ant-form {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #1f2937;
    }

    .ant-input,
    .ant-input-password,
    .ant-select-selector {
      border-radius: 4px;
      border-color: #d9d9d9;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus,
      &-focused {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ant-input-affix-wrapper {
      border-radius: 4px;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus,
      &-focused {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  .ant-btn {
    border-radius: 4px;

    &.ant-btn-primary {
      background-color: #1890ff;
      border-color: #1890ff;

      &:hover,
      &:focus {
        background-color: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.ant-btn-danger {
      background-color: #ff4d4f;
      border-color: #ff4d4f;

      &:hover,
      &:focus {
        background-color: #ff7875;
        border-color: #ff7875;
      }
    }
  }

  .ant-tag {
    border-radius: 4px;
    padding: 2px 8px;
    margin-right: 8px;

    &.ant-tag-blue {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }

    &.ant-tag-green {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }

    &.ant-tag-red {
      background-color: #fff1f0;
      border-color: #ffa39e;
      color: #f5222d;
    }
  }
}
