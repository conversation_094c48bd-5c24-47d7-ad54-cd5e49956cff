import React from "react";
import { Result, Button } from "antd";
import { useNavigate } from "react-router-dom";

const NoAccessResult: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="ai-quotation-container">
      <div className="ai-quotation-content">
        <Result
          status="403"
          title="无权访问"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => navigate("/")}>
              返回首页
            </Button>
          }
        />
      </div>
    </div>
  );
};

export default NoAccessResult;
