import { get, post, del } from "@/utils/request";

// 获取所有国际价格
export const getInternationalPriceList = (data?: any) => {
  return get("/getInternationalPriceList", data);
};

export const getInternationalPriceByCondition = (data: any) => {
  return post("/getInternationalPriceByCondition", data);
};
export const getUsersByDepartment = (data: any) => {
  return post("/getUserByCondition", data);
};

export const addInternationalPrice = (data: any) => {
  return post("/addInternationalPrice", data);
};

export const updateInternationalPrice = (data: any) => {
  return post("/updateInternationalPrice", data);
};

export const delInternationalPrice = (data: any) => {
  return del("/delInternationalPrice", data);
};

export const getAllAirlineCompany = () => {
  return get("/getAllAirlineCompany");
};

export const getAllPort = () => {
  return get("/getAllPort");
};

export const getAllShipmentPlace = () => {
  return get("/getAllShipmentPlace");
};

export const getAllSpecialitems = () => {
  return get("/getAllSpecialitems");
};

export const getAllPackageType = () => {
  return get("/getAllPackageType");
};
