import React from "react";
import { Modal, Form, Input, message } from "antd";
import { FormInstance } from "antd/es/form";
import { useTranslation } from "react-i18next";
import {
  addOtherfeesName,
  updateOtherfeesName,
} from "@/services/baseDataService";
import { refreshMiscellaneousFees } from "@/utils/refreshBaseData";

interface ActionModalType {
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  method: string;
  form: FormInstance;
  queryMiscellaneousFeeList: () => void;
}
const ActionModal: React.FC<ActionModalType> = ({
  isModalOpen,
  setIsModalOpen,
  method,
  form,
  queryMiscellaneousFeeList,
}) => {
  const { t } = useTranslation();
  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        const res =
          method === "edit"
            ? await updateOtherfeesName(values)
            : await addOtherfeesName(values);
        const { data } = res;
        if (data.resultCode === 200) {
          // 刷新Redux中的其他费用数据
          await refreshMiscellaneousFees();
          // 刷新页面数据
          queryMiscellaneousFeeList();
          setIsModalOpen(false);
          message.success(
            method === "edit"
              ? t(
                  "dataManagement.miscellaneousFeeManagement.messages.updateSuccess"
                )
              : t(
                  "dataManagement.miscellaneousFeeManagement.messages.addSuccess"
                )
          );
        } else {
          message.error(
            method === "edit"
              ? t(
                  "dataManagement.miscellaneousFeeManagement.messages.updateFailed"
                )
              : t(
                  "dataManagement.miscellaneousFeeManagement.messages.addFailed"
                )
          );
        }
      })
      .catch((errorInfo) => {
        console.log("Validation failed:", errorInfo);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal
      title={
        method === "add"
          ? t("dataManagement.miscellaneousFeeManagement.modal.title.add")
          : t("dataManagement.miscellaneousFeeManagement.modal.title.edit")
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
    >
      <Form name="basic" layout="vertical" form={form}>
        {
          <Form.Item
            label={t(
              "dataManagement.miscellaneousFeeManagement.modal.fields.id"
            )}
            name="otherfeesid"
            style={{ display: "none" }}
          >
            <Input disabled />
          </Form.Item>
        }

        <Form.Item
          label={t(
            "dataManagement.miscellaneousFeeManagement.modal.fields.miscellaneousFeeName"
          )}
          name="feesname"
          rules={[
            {
              required: true,
              message: t(
                "dataManagement.miscellaneousFeeManagement.modal.validation.miscellaneousFeeNameRequired"
              ),
            },
            {
              max: 100,
              message: t(
                "dataManagement.miscellaneousFeeManagement.modal.validation.miscellaneousFeeNameMaxLength"
              ),
            },
          ]}
        >
          <Input
            placeholder={t(
              "dataManagement.miscellaneousFeeManagement.modal.placeholders.miscellaneousFeeName"
            )}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ActionModal;
