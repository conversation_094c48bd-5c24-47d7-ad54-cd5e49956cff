@import "../../pages/SmartQuotation/index.less";

.saved-quotations-container {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(118, 199, 242, 0.2);
    border-radius: 3px;

    &:hover {
      background: rgba(33, 136, 232, 0.3);
    }
  }

  .quotation-list-item {
    padding: 8px 0;
    border-bottom: none;
  }

  .saved-quotation-card {
    width: 100%;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(118, 199, 242, 0.2);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 16px rgba(118, 199, 242, 0.15);
      border-color: rgba(118, 199, 242, 0.4);
    }

    .ant-card-body {
      padding: 16px;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .airline-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .airline-name {
          font-size: 16px;
          color: @text-color;
        }

        .recommendation-badge {
          .ant-badge-status-dot {
            background: #76c7f2;
            box-shadow: 0 0 8px rgba(118, 199, 242, 0.5);
            animation: pulseBadge 2s infinite;
          }

          .ant-badge-status-text {
            color: #76c7f2;
            font-size: 12px;
            font-weight: 500;
          }
        }
      }

      .favorite-action {
        .favorite-icon {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.25);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            color: #faad14;
            transform: scale(1.1);
          }

          &.active {
            color: #faad14;
          }
        }
      }
    }

    .route-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 6px;
      border: 1px solid rgba(118, 199, 242, 0.1);

      .route-points {
        display: flex;
        align-items: center;
        gap: 8px;

        .origin,
        .destination {
          font-weight: 500;
          color: @text-color;
        }

        .route-arrow {
          color: #76c7f2;
          font-weight: bold;
        }
      }

      .price-tag {
        display: flex;
        align-items: baseline;
        gap: 4px;
        background: rgba(118, 199, 242, 0.1);
        padding: 4px 8px;
        border-radius: 4px;

        .anticon {
          color: #76c7f2;
          font-size: 12px;
        }

        .ant-typography {
          color: #1797e1;
          font-size: 16px;
        }

        .currency {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }

    .quotation-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .ant-tag {
        margin-right: 0;
        display: flex;
        align-items: center;
        gap: 4px;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        .anticon {
          font-size: 12px;
        }
      }
    }

    .validity-info {
      margin-bottom: 12px;
      font-size: 12px;
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 4px;
      padding-top: 8px;
      border-top: 1px solid rgba(118, 199, 242, 0.1);

      .ant-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(118, 199, 242, 0.1);
        }

        &.ant-btn-dangerous:hover {
          background: rgba(255, 77, 79, 0.1);
        }
      }
    }
  }
}

.saved-quotations-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  .ant-empty {
    margin-bottom: 16px;
  }

  .empty-tip {
    color: rgba(0, 0, 0, 0.45);
    max-width: 300px;
    text-align: center;
  }
}

.delete-quotation-info {
  background: rgba(0, 0, 0, 0.02);
  padding: 12px;
  border-radius: 4px;
  margin-top: 16px;

  .ant-typography {
    display: block;
    line-height: 1.5;

    &:first-child {
      margin-bottom: 4px;
    }
  }
}

@keyframes pulseBadge {
  0% {
    box-shadow: 0 0 0 0 rgba(118, 199, 242, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(118, 199, 242, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(118, 199, 242, 0);
  }
}
