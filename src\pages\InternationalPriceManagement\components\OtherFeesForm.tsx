import React from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Button,
  Row,
  Col,
} from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { FormInstance } from "antd/es/form";
import useBaseData from "@/hooks/useBaseData";

interface OtherFeesFormProps {
  form: FormInstance;
}

const OtherFeesForm: React.FC<OtherFeesFormProps> = ({ form }) => {
  const { t } = useTranslation();

  const { miscellaneousFeeOptions } = useBaseData();

  return (
    <div className="other-fees-section">
      <Form.List name="otherfees">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} className="other-fee-item">
                <Row gutter={[12, 0]} align="middle">
                  <Col xs={24} sm={12} md={5} lg={5} xl={5}>
                    <Form.Item
                      {...restField}
                      name={[name, "feesname"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.feeName"
                      )}
                      rules={[
                        {
                          required: true,
                          message: t(
                            "internationalPriceManagement.actionModal.validation.inputFeeName"
                          ),
                        },
                      ]}
                    >
                      <Select
                        className="input-default"
                        placeholder={t(
                          "internationalPriceManagement.actionModal.placeholders.inputFeeName"
                        )}
                        showSearch
                        optionFilterProp="label"
                        options={miscellaneousFeeOptions}
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                    <Form.Item
                      {...restField}
                      name={[name, "billingmethod"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.billingMethod"
                      )}
                      initialValue={1}
                    >
                      <Select
                        className="input-default"
                        options={[
                          {
                            label: t(
                              "internationalPriceManagement.actionModal.billingMethods.perOrder"
                            ),
                            value: 1,
                          },
                          {
                            label: t(
                              "internationalPriceManagement.actionModal.billingMethods.perKg"
                            ),
                            value: 2,
                          },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, currentValues) => {
                      const prevBillingMethod =
                        prevValues?.otherfees?.[name]?.billingmethod;
                      const currentBillingMethod =
                        currentValues?.otherfees?.[name]?.billingmethod;
                      return prevBillingMethod !== currentBillingMethod;
                    }}
                  >
                    {({ getFieldValue }) => {
                      const billingMethod = getFieldValue([
                        "otherfees",
                        name,
                        "billingmethod",
                      ]);
                      return billingMethod === 1 ? (
                        <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                          <Form.Item
                            {...restField}
                            name={[name, "orderfees"]}
                            label={t(
                              "internationalPriceManagement.actionModal.fields.orderFees"
                            )}
                            rules={[
                              {
                                required: true,
                                message: t(
                                  "internationalPriceManagement.actionModal.validation.inputOrderFees"
                                ),
                              },
                            ]}
                          >
                            <InputNumber
                              className="input-default"
                              min={0}
                              precision={1}
                              placeholder={t(
                                "internationalPriceManagement.actionModal.placeholders.inputOrderFees"
                              )}
                            />
                          </Form.Item>
                        </Col>
                      ) : (
                        <>
                          <Col xs={24} sm={12} md={3} lg={3} xl={3}>
                            <Form.Item
                              {...restField}
                              name={[name, "weightfees"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.weightFees"
                              )}
                              rules={[
                                {
                                  required: true,
                                  message: t(
                                    "internationalPriceManagement.actionModal.validation.inputWeightFees"
                                  ),
                                },
                              ]}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputWeightFees"
                                )}
                              />
                            </Form.Item>
                          </Col>
                          <Col xs={24} sm={12} md={4} lg={4} xl={4}>
                            <Form.Item
                              {...restField}
                              name={[name, "mincharges"]}
                              label={t(
                                "internationalPriceManagement.actionModal.fields.minCharges"
                              )}
                              rules={[
                                {
                                  required: true,
                                  message: t(
                                    "internationalPriceManagement.actionModal.validation.inputMinCharges"
                                  ),
                                },
                              ]}
                            >
                              <InputNumber
                                className="input-default"
                                min={0}
                                precision={1}
                                placeholder={t(
                                  "internationalPriceManagement.actionModal.placeholders.inputMinCharges"
                                )}
                              />
                            </Form.Item>
                          </Col>
                        </>
                      );
                    }}
                  </Form.Item>
                  <Col xs={24} sm={12} md={3} lg={3} xl={3}>
                    <Form.Item
                      {...restField}
                      name={[name, "ischarges"]}
                      label={t(
                        "internationalPriceManagement.actionModal.fields.isCharges"
                      )}
                      valuePropName="checked"
                      initialValue={false}
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <MinusCircleOutlined
                    className="icon-remove"
                    onClick={() => remove(name)}
                    style={{
                      fontSize: "16px",
                      color: "#ff4d4f",
                      marginLeft: "10px",
                    }}
                  />
                </Row>
              </div>
            ))}
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
              className="btn-dashed"
              style={{ marginTop: "16px" }}
            >
              {t(
                "internationalPriceManagement.actionModal.buttons.addOtherFee"
              )}
            </Button>
          </>
        )}
      </Form.List>
    </div>
  );
};

export default OtherFeesForm;
