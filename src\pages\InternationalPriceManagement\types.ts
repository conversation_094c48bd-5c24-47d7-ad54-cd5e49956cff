// 舱报信息类型定义
export interface CabinReport {
  date: string | number;
  transferdate?: string | number;
  weightlimit: number;
  volumelimit: number;
  lowerdensity: number;
  upperdensity: number;
  onlyparts: boolean;
  singleinquiry: boolean;
  pricechanges: PriceChange[];
  specialprice: SpecialPrice[];
}

// 价格变化类型定义
export interface PriceChange {
  leftdensity: number;
  rightdensity: number;
  pchanges: number;
}

// 特价找货类型定义
export interface SpecialPrice {
  densityupperlimit: number;
  densitylowerlimit: number;
  sprice: number;
}

// 密度价格区间类型定义
export interface IntervalPrice {
  densitylvalue: number;
  densityrvalue: number;
  q100: number;
  q300: number;
  q500: number;
  q1000: number;
}

// 国际价格类型定义
export interface InternationalPrice {
  priceid: number;
  userid: number;
  departmentid: number;
  supplierid?: number;
  suppliername: string;
  pricecreatetime: string;
  priceupdatetime: string;
  effectivetime: string;
  specialitems: string;
  specialcharges: any;
  airlinename: string;
  originport: string;
  originschedules: string;
  unloadingport: string;
  istransfer: boolean;
  transfer: string;
  transferschedules: string;
  mprice: number;
  nprice: number;
  q45_sub: boolean;
  subq45price: number;
  q45price: number;
  q100_sub: boolean;
  subq100price: number;
  lengthlimit: number;
  widthlimit: number;
  heightlimit: number;
  singleweightlimit: number;
  acceptbrand: boolean;
  iscabin: boolean;
  cabinprice: number;
  packagecharges: any;
  smallcargofee: number;
  cabinreport?: CabinReport[];
  intervalprice?: IntervalPrice[];
  [key: string]: any;
}
