import { post, get, del } from "@/utils/request";

export const LoginApi = (
  data: { password: string; email: string } | undefined
) => {
  return post("/verifyUserByPassword", data);
};

// 发送邮箱验证码
export const getCode = (data: { email: string }) => {
  return get("/getCode", data);
};

// 注册新用户
export const registerAccount = (data: any) => {
  return post("/registerAccount", data);
};

// 注销账号
export const delAccount = (data: any) => {
  return post("/delAccount", data);
};
