.login-container {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f2fc 100%);
  perspective: 1000px;

  .language-switcher-wrapper {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;

    .language-switcher-btn {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(23, 151, 225, 0.2);
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 1);
        border-color: rgba(23, 151, 225, 0.4);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(rgba(23, 151, 225, 0.12) 1px, transparent 1px),
      linear-gradient(90deg, rgba(23, 151, 225, 0.12) 1px, transparent 1px);
    background-size: 30px 30px;
    transform: perspective(500px) rotateX(60deg) scale(3, 3) translateZ(-100px);
    transform-origin: center center;
    opacity: 1;
    z-index: 0;
  }

  .light-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    z-index: 0;
    opacity: 0.5;
  }

  .light-orb-1 {
    width: 500px;
    height: 500px;
    background: radial-gradient(
      circle,
      rgba(23, 151, 225, 0.4) 0%,
      rgba(23, 151, 225, 0) 70%
    );
    top: -150px;
    right: -150px;
    animation: float-orb 15s infinite ease-in-out;
  }

  .light-orb-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(
      circle,
      rgba(64, 169, 255, 0.35) 0%,
      rgba(64, 169, 255, 0) 70%
    );
    bottom: -100px;
    left: -100px;
    animation: float-orb 18s infinite ease-in-out reverse;
  }

  .light-orb-3 {
    width: 350px;
    height: 350px;
    background: radial-gradient(
      circle,
      rgba(23, 151, 225, 0.3) 0%,
      rgba(23, 151, 225, 0) 70%
    );
    top: 40%;
    left: 5%;
    animation: float-orb 20s infinite ease-in-out 2s;
  }

  .particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
  }

  .particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(23, 151, 225, 0.3);
    border-radius: 50%;
    animation: particle-float 15s infinite ease-in-out;
    box-shadow: 0 0 8px rgba(23, 151, 225, 0.4);
  }

  .particle-1 {
    top: 20%;
    left: 20%;
    animation-duration: 15s;
    animation-delay: 0s;
  }

  .particle-2 {
    top: 70%;
    left: 30%;
    animation-duration: 18s;
    animation-delay: 1s;
  }

  .particle-3 {
    top: 30%;
    left: 60%;
    animation-duration: 20s;
    animation-delay: 2s;
  }

  .particle-4 {
    top: 80%;
    left: 70%;
    animation-duration: 22s;
    animation-delay: 3s;
  }

  .particle-5 {
    top: 40%;
    left: 80%;
    animation-duration: 25s;
    animation-delay: 4s;
  }

  .particle-6 {
    top: 10%;
    left: 50%;
    animation-duration: 30s;
    animation-delay: 5s;
  }

  .particle-7 {
    top: 50%;
    left: 90%;
    animation-duration: 35s;
    animation-delay: 6s;
  }

  .particle-8 {
    top: 60%;
    left: 10%;
    animation-duration: 40s;
    animation-delay: 7s;
  }

  .particle-9 {
    top: 90%;
    left: 40%;
    animation-duration: 45s;
    animation-delay: 8s;
  }

  .particle-10 {
    top: 25%;
    left: 75%;
    animation-duration: 50s;
    animation-delay: 9s;
  }

  .decoration-cube {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(23, 151, 225, 0.2);
    box-shadow: 0 0 15px rgba(23, 151, 225, 0.15);
    transform-style: preserve-3d;
    animation: cube-rotate 20s infinite linear;
    z-index: 0;
  }

  .cube-1 {
    width: 60px;
    height: 60px;
    top: 15%;
    right: 15%;
    animation-duration: 25s;
  }

  .cube-2 {
    width: 40px;
    height: 40px;
    bottom: 20%;
    right: 25%;
    animation-duration: 30s;
    animation-delay: 5s;
  }

  .cube-3 {
    width: 50px;
    height: 50px;
    top: 70%;
    left: 20%;
    animation-duration: 35s;
    animation-delay: 2s;
  }

  .decoration-sphere {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(
      135deg,
      rgba(23, 151, 225, 0.15) 0%,
      rgba(64, 169, 255, 0.08) 100%
    );
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(23, 151, 225, 0.15);
    animation: sphere-float 15s infinite ease-in-out;
    z-index: 0;
  }

  .sphere-1 {
    width: 80px;
    height: 80px;
    top: 25%;
    left: 20%;
    animation-duration: 20s;
  }

  .sphere-2 {
    width: 60px;
    height: 60px;
    bottom: 15%;
    right: 10%;
    animation-duration: 25s;
    animation-delay: 3s;
  }
}

.login-box {
  width: 420px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: @border-radius;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.05),
    0 20px 30px -10px rgba(23, 151, 225, 0.15);
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transform: translateY(0);
  transition: all 0.4s ease;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: @primary-gradient;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 10%;
    right: 10%;
    height: 1px;
    background: linear-gradient(
      to right,
      rgba(23, 151, 225, 0),
      rgba(23, 151, 225, 0.5),
      rgba(23, 151, 225, 0)
    );
  }

  .title {
    text-align: center;
    margin-bottom: 36px;
    position: relative;

    &::after {
      content: "";
      display: block;
      width: 40px;
      height: 3px;
      background: @primary-gradient;
      margin: 12px auto 0;
      border-radius: 3px;
      animation: pulse 2s infinite;
    }

    h1 {
      font-size: 28px;
      font-weight: 700;
      color: @text-primary;
      margin: 0;
      background: linear-gradient(135deg, #1797e1 0%, #40a9ff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: 0.5px;
      text-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
      transform: translateZ(0);
    }

    p {
      font-size: 14px;
      color: @text-secondary;
      margin-top: 8px;
      opacity: 0.8;
    }
  }

  .ant-form-item {
    margin-bottom: 32px;
    position: relative;

    .ant-form-item-label {
      padding-bottom: 6px;

      label {
        font-size: 14px;
        font-weight: 500;
        color: @text-primary;

        &::before {
          display: none !important;
        }

        &::after {
          content: "*";
          color: #ff4d4f;
          margin-left: 4px;
          display: inline-block;
        }
      }
    }

    .ant-form-item-explain-error {
      color: #ff4d4f;
      font-size: 14px;
      margin-top: 4px;
      font-weight: 500;
    }

    .ant-input {
      height: 46px;
      border-radius: 8px;
      border: 1px solid rgba(229, 231, 235, 0.8);
      transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      background-color: rgba(249, 250, 251, 0.8);
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);

      &:hover {
        border-color: rgba(23, 151, 225, 0.5);
        background-color: rgba(255, 255, 255, 0.9);
      }

      &:focus {
        border-color: rgba(23, 151, 225, 0.8);
        box-shadow:
          0 0 0 3px rgba(23, 151, 225, 0.1),
          0 2px 8px rgba(0, 0, 0, 0.05);
        background-color: #fff;
      }
    }

    .ant-input-affix-wrapper {
      height: 46px;
      border-radius: 8px;
      border: 1px solid rgba(229, 231, 235, 0.8);
      transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      padding: 0 11px;
      background-color: rgba(249, 250, 251, 0.8);
      backdrop-filter: blur(4px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);

      &:hover {
        border-color: rgba(23, 151, 225, 0.5);
        background-color: rgba(255, 255, 255, 0.9);
      }

      &:focus,
      &-focused {
        border-color: rgba(23, 151, 225, 0.8);
        box-shadow:
          0 0 0 3px rgba(23, 151, 225, 0.1),
          0 2px 8px rgba(0, 0, 0, 0.05);
        background-color: #fff;
      }

      .ant-input {
        height: auto;
        border: none;
        box-shadow: none;
        padding: 0;
        background-color: transparent;

        &:hover,
        &:focus {
          box-shadow: none;
          background-color: transparent;
        }
      }

      .ant-input-prefix {
        margin-right: 10px;
        color: #9ca3af;
      }

      .ant-input-suffix {
        .anticon {
          color: #9ca3af;

          &:hover {
            color: @primary-color;
          }
        }
      }
    }
  }

  .login-button {
    margin-top: 12px;

    .ant-btn {
      width: 100%;
      height: 46px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      letter-spacing: 2px;
      background: linear-gradient(135deg, #1797e1 0%, #40a9ff 100%);
      border: none;
      box-shadow:
        0 4px 12px rgba(23, 151, 225, 0.2),
        0 0 0 0 rgba(23, 151, 225, 0.5);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        transform: translateX(-100%);
        transition: transform 1s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          0 8px 25px rgba(23, 151, 225, 0.3),
          0 0 0 5px rgba(23, 151, 225, 0.1);

        &::before {
          transform: translateX(100%);
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 4px 12px rgba(23, 151, 225, 0.2);
      }
    }
  }

  .register-link {
    text-align: center;
    margin-top: 16px;
    color: @text-secondary;

    span {
      margin-right: 4px;
    }

    .ant-btn-link {
      padding: 0;
      font-weight: 500;
      color: @primary-color;

      &:hover {
        color: darken(@primary-color, 10%);
      }
    }
  }

  .register-modal {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .ant-modal-header {
      padding: 16px 24px;
      background: linear-gradient(to right, #f0f7ff, #f9fcff);
      border-bottom: 1px solid #e6f0fa;

      .ant-modal-title {
        font-weight: 600;
        color: @text-primary;
        font-size: 16px;
      }
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-form-item-label > label {
      font-weight: 500;
      color: @text-primary;
    }

    .ant-input,
    .ant-input-affix-wrapper {
      height: 40px;
      border-radius: 6px;
      transition: all 0.3s;

      &:hover,
      &:focus {
        border-color: @primary-color;
      }
    }

    .register-button {
      margin-top: 24px;

      .ant-btn {
        height: 40px;
        font-weight: 600;
        background: linear-gradient(135deg, #1797e1 0%, #40a9ff 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(23, 151, 225, 0.2);
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(23, 151, 225, 0.3);
        }
      }
    }
  }

  .footer {
    text-align: center;
    margin-top: 32px;
    font-size: 12px;
    color: #9ca3af;

    a {
      color: @primary-color;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@keyframes grid-animation {
  0% {
    transform: perspective(500px) rotateX(60deg) scale(2.5, 2.5)
      translateZ(-200px);
  }
  50% {
    transform: perspective(500px) rotateX(65deg) scale(2.5, 2.5)
      translateZ(-220px);
  }
  100% {
    transform: perspective(500px) rotateX(60deg) scale(2.5, 2.5)
      translateZ(-200px);
  }
}

@keyframes float-orb {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

@keyframes particle-float {
  0%,
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translate3d(30px, 30px, 10px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translate3d(-20px, 40px, -10px) scale(0.8);
    opacity: 0.4;
  }
  75% {
    transform: translate3d(10px, -30px, 20px) scale(1.1);
    opacity: 0.7;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.7;
    transform: scaleX(0.8);
  }
}

@keyframes cube-rotate {
  0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
  }
}

@keyframes sphere-float {
  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) translateX(10px) scale(1.05);
    opacity: 0.9;
  }
  50% {
    transform: translateY(10px) translateX(15px) scale(0.95);
    opacity: 0.6;
  }
  75% {
    transform: translateY(15px) translateX(-10px) scale(1.02);
    opacity: 0.8;
  }
}
