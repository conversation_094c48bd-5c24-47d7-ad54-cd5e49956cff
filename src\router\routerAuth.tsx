import { Navigate, useLocation } from "react-router-dom";
import React, { ReactNode } from "react";

interface RouterAuthProps {
  children: ReactNode;
}

interface RoleConfig {
  allowedPaths: string[];
  defaultPath: string;
}

// 用户角色权限映射表
const ROLE_PERMISSIONS: Record<number, RoleConfig> = {
  // 普通用户 - 只能访问AI智能报价、智能报价、智能报价2.0、3D智能报价和个人报价页面
  0: {
    allowedPaths: ["/ai_quotation", "/personal_quotation", "/smart_quotation"],
    defaultPath: "/ai_quotation",
  },
  // 询价业务员 - 只能访问询价管理、供应价格筛选、国内价格管理与国际价格管理页面
  1: {
    allowedPaths: [
      "/quotation",
      "/supply_price",
      "/domestic_price",
      "/international_price",
      "/intelligenty_quotation",
    ],
    defaultPath: "/quotation",
  },
  // 价格业务员 - 可以访问价格管理下的国内价格管理、国际价格管理页面、特殊报价管理页面以及基础数据管理下的六个页面
  2: {
    allowedPaths: [
      "/domestic_price",
      "/international_price",
      "/manual_quotation",
      "/airline_management",
      "/harbor_management",
      "/package_type_management",
      "/special_items_management",
      "/shipment_place_management",
      "/supplier_management",
      "/miscellaneous_fee_management",
    ],
    defaultPath: "/international_price",
  },
  // 询价部门主管 - 只能访问询价管理、国内价格管理、国际价格管理和供应价格筛选页面
  3: {
    allowedPaths: [
      "/quotation",
      "/domestic_price",
      "/international_price",
      "/supply_price",
      "/intelligenty_quotation",
    ],
    defaultPath: "/quotation",
  },
  // 价格部门主管 - 可以访问国内价格管理、国际价格管理页面、特殊报价管理页面以及基础数据管理下的五个页面
  4: {
    allowedPaths: [
      "/domestic_price",
      "/international_price",
      "/manual_quotation",
      "/airline_management",
      "/harbor_management",
      "/package_type_management",
      "/special_items_management",
      "/shipment_place_management",
      "/supplier_management",
      "/miscellaneous_fee_management",
    ],
    defaultPath: "/international_price",
  },
  // 超级管理员 - 只能访问组织管理和基础数据管理下的五个页面
  5: {
    allowedPaths: [
      // "/quotation",
      // "/supply_price",
      // "/domestic_price",
      // "/international_price",
      // "/domestic_price",
      // "/manual_quotation",
      "/organization_manage",
      "/airline_management",
      "/harbor_management",
      "/package_type_management",
      "/special_items_management",
      "/shipment_place_management",
      "/supplier_management",
      "/miscellaneous_fee_management",
      // "/intelligenty_quotation",
    ],
    defaultPath: "/organization_manage",
  },
};

export const RouterAuth: React.FC<RouterAuthProps> = ({ children }) => {
  const token = localStorage.getItem("token");
  const location = useLocation();
  const currentPath = location.pathname;

  let user = null;
  try {
    user = JSON.parse(localStorage.getItem("user") || "null");
  } catch (e) {
    console.error("Failed to parse user from localStorage", e);
  }

  if (!token) {
    return <Navigate to="/login" replace />;
  }

  const userIdentity = user?.useridentity;

  // 检查用户权限
  if (userIdentity !== undefined && ROLE_PERMISSIONS[userIdentity]) {
    const { allowedPaths, defaultPath } = ROLE_PERMISSIONS[userIdentity];

    const isAllowed = allowedPaths.some(
      (path) => currentPath === path || currentPath.startsWith(path + "/")
    );

    // 如果不在允许的路径列表中且不是根路径，重定向到默认页面
    if (!isAllowed && currentPath !== "/") {
      return <Navigate to={defaultPath} replace />;
    }
  }

  return <>{children}</>;
};
