import { configureStore } from "@reduxjs/toolkit";
import counterReducer from "./slices/counterSlice";
import userReducer from "./slices/userSlice";
import baseDataReducer from "./slices/baseDataSlice";
import languageReducer from "./slices/languageSlice";

export const store = configureStore({
  reducer: {
    counter: counterReducer,
    user: userReducer,
    baseData: baseDataReducer,
    language: languageReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export default store;
