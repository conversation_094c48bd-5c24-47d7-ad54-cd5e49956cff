@primary-color: #1797e1;
@border-radius-base: 6px;
@link-color: #1da57a;
@font-size-base: 16px;
@transparent-primary-color: #1797e142;
@secondary-color: #76c7f2;
@accent-color: #d0d9e2;
@dark-color: #0d1b30;
@light-color: #ffffff;
@text-color: #333333;
@primary-gradient: linear-gradient(135deg, #52cafe, #2188e8);
@secondary-gradient: linear-gradient(135deg, #76c7f2, #52cafe);
@dark-gradient: linear-gradient(135deg, #0d1b30, #1a365d);
@futuristic-gradient: linear-gradient(
  90deg,
  rgba(82, 202, 254, 0.1),
  rgba(33, 136, 232, 0.1)
);
@holographic-gradient: linear-gradient(
  135deg,
  rgba(255, 255, 255, 0.1),
  rgba(255, 255, 255, 0.05)
);
@glow-effect: 0 0 15px rgba(82, 202, 254, 0.4);
@neon-glow: 0 0 10px rgba(33, 136, 232, 0.5);
@futuristic-border: 1px solid rgba(82, 202, 254, 0.3);
@glass-effect: rgba(255, 255, 255, 0.05);
@glass-border: 1px solid rgba(255, 255, 255, 0.1);
@bg-color: #f9fafb;
@card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
@primary-text: #1f2937;
@secondary-text: #4b5563;
@border-color: #e5e7eb;
@rounded-button: 6px;
@transition-time: 0.3s;
@section-border-radius: 8px;
@box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
@text-primary: #1f2937;
@text-secondary: #4b5563;
@border-radius: 12px;
@hover-bg: #f5f7fa;
@active-border: 2px solid @primary-color;
@card-glow: 0 0 10px rgba(0, 120, 255, 0.2);
@card-glow-hover: 0 0 12px rgba(0, 120, 255, 0.4);
@card-glow-active: 0 0 15px rgba(0, 120, 255, 0.4);
@large-screen: ~"(min-width: 1600px)";
@extra-large-screen: ~"(min-width: 1920px)";
