import React, { useState, useEffect } from "react";
import "./index.less";
import TableCom from "@/components/TableCom";
import { Space, Button, Form, Popconfirm, Tooltip, message } from "antd";
import type { TableProps } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { delOtherfeesName } from "@/services/baseDataService";
import ActionModal from "./components/ActionModal";
import useBaseData from "@/hooks/useBaseData";

const MiscellaneousFeeManagement: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [method, setMethod] = useState<string>("");

  const { miscellaneousFees: data, loadMiscellaneousFees } = useBaseData();

  useEffect(() => {
    loadMiscellaneousFees();
  }, []);

  const columns: TableProps<any>["columns"] = [
    {
      title: t(
        "dataManagement.miscellaneousFeeManagement.columns.miscellaneousFeeName"
      ),
      dataIndex: "feesname",
      key: "feesname",
    },
    {
      title: t("dataManagement.miscellaneousFeeManagement.columns.actions"),
      key: "actions",
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          <Tooltip
            title={t("dataManagement.miscellaneousFeeManagement.actions.edit")}
          >
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t(
              "dataManagement.miscellaneousFeeManagement.deleteConfirm.title"
            )}
            description={t(
              "dataManagement.miscellaneousFeeManagement.deleteConfirm.description"
            )}
            onConfirm={() => handleDelete(record)}
            okText={t(
              "dataManagement.miscellaneousFeeManagement.deleteConfirm.okText"
            )}
            cancelText={t(
              "dataManagement.miscellaneousFeeManagement.deleteConfirm.cancelText"
            )}
          >
            <Tooltip
              title={t(
                "dataManagement.miscellaneousFeeManagement.actions.delete"
              )}
            >
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setMethod("add");
    form.resetFields();
    setIsModalOpen(true);
  };

  const handleEdit = (record: any) => {
    setMethod("edit");
    form.setFieldsValue(record);
    setIsModalOpen(true);
  };

  const handleDelete = async (record: any) => {
    try {
      const res = await delOtherfeesName({ otherfeesid: record.otherfeesid });
      const { data } = res;
      if (data.resultCode === 200) {
        loadMiscellaneousFees(true);
        message.success(
          t("dataManagement.miscellaneousFeeManagement.messages.deleteSuccess")
        );
      } else {
        message.error("删除失败");
      }
    } catch (error) {
      console.error("删除供应商失败", error);
      message.error("删除失败");
    }
  };
  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("dataManagement.miscellaneousFeeManagement.title")}
        </div>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            {t("dataManagement.miscellaneousFeeManagement.newMiscellaneousFee")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="miscellaneous-fee-management">
      <TableCom<any>
        columns={columns}
        data={data}
        filterRender={filterRender}
      />
      <ActionModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        method={method}
        form={form}
        queryMiscellaneousFeeList={() => loadMiscellaneousFees(true)}
      />
    </div>
  );
};

export default MiscellaneousFeeManagement;
