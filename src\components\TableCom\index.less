.table_com {
  .filter_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      position: relative;
      padding-left: 12px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background-color: #1797e1;
        border-radius: 2px;
      }
    }
  }

  .scroll-table {
    &.empty-table {
      .ant-table-container {
        overflow-x: hidden !important;

        .ant-table-content {
          overflow-x: hidden !important;
        }

        .ant-table-body {
          overflow-x: hidden !important;
        }
      }
    }

    .ant-table {
      min-width: 50vh;
      .ant-table-body,
      .ant-table-content {
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;
        scrollbar-gutter: stable;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #eaeaea;
          border-radius: 4px;
          border: 2px solid transparent;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: #d0d0d0;
        }
      }
    }
  }

  .standard-table {
    .ant-table-thead > tr > th {
      background-color: #f9fafb;
    }

    .ant-table-tbody > tr.ant-table-row:hover > td {
      background-color: #e6f7ff;
    }

    .ant-table-row-selected > td {
      background-color: #e6f7ff !important;
    }
  }

  .delete {
    color: #ff4d4f;
  }

  .edit-button {
    color: #1797e1;
  }
}
