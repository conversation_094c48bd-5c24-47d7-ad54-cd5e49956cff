import React, { useState, useEffect } from "react";
import {
  Table,
  Space,
  Button,
  Input,
  Popconfirm,
  message,
  Tag,
  Tooltip,
  Tabs,
} from "antd";
import "./index.less";
import type { TableProps, GetProp, TableColumnType } from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  InboxOutlined,
} from "@ant-design/icons";

import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";
import QuotationEditor from "./components/QuotationEditor";
import { useTranslation } from "react-i18next";

type TablePaginationConfig = Exclude<
  GetProp<TableProps, "pagination">,
  boolean
>;

type DataIndex = keyof DataType;

interface DataType {
  inquiryid: string;
  freightmethod: string; // 货运方式
  goodstype: string; // 货物类型
  tradeterms: string; // 贸易术语
  companyname: string; // 公司名
  inquirer: string; // 询价人
  inquirytime: string; // 询价时间
  shippedplace: string; // 发货地
  originport: string; // POL: 起始港
  unloadingport: string; // POD: 目的港
  grossweight: number; // 货物毛重
  goodsvolume: number; // 货物体积
  singlemaxweight?: number | null; // 单件货最大重量
  isbrand: boolean; // 是否品牌货
  ensurecabin: boolean; // 保舱
  isvalidity: boolean; // 要求ETD(发货日期)
  shipmentdate?: string; // 发货日期
  packagetype: string; // 包装形式
  cargolength?: number; // 货物长度
  cargowidth?: number; // 货物宽
  cargoheight?: number; // 货物高
  specialcargo?: string; // 特殊货物
  inquirycreatetime: string; // 记录生成时间
  inquirystate: number; // 询价状态
  userid: number; // 用户ID
  departmentid: number; // 所在部门id
  status?: "quoted" | "unquoted" | "deal" | "nodeal"; // 状态：已报价、未报价、已成交、未成交
  [key: string]: any;
}

const ManualQuotation: React.FC = () => {
  const navigate = useNavigate();
  const { currentLanguage } = useAppSelector((state) => state.language);
  const [quotationData, setQuotationData] = useState<DataType[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [quotationEditorVisible, setQuotationEditorVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<DataType | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const { t } = useTranslation();

  const isEnglish = currentLanguage === "en-US";

  const fetchManualQuotationList = async () => {
    try {
      setLoading(true);
      setTimeout(() => {
        setQuotationData([]);
        setLoading(false);
        // message.success("获取人工报价列表成功");
      }, 500);
    } catch (e) {
      console.log(e);
      // message.error("获取人工报价列表失败");
    }
  };

  useEffect(() => {
    fetchManualQuotationList();
  }, []);

  const filteredData = quotationData;

  const getColumnSearchProps = (
    dataIndex: DataIndex
  ): TableColumnType<DataType> => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
    }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        <Input
          placeholder={t("manualQuotation.filters.search")}
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => confirm()}
          style={{ marginBottom: 8, display: "block" }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => confirm()}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            {t("manualQuotation.filters.confirm")}
          </Button>
          <Button
            onClick={() => {
              clearFilters && clearFilters();
              confirm();
            }}
            size="small"
            style={{ width: 90 }}
          >
            {t("manualQuotation.filters.reset")}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
    ),
    onFilter: (value: any, record: DataType) => {
      const recordValue = record[dataIndex];
      if (!recordValue) return false;
      if (typeof value === "string") {
        return recordValue
          .toString()
          .toLowerCase()
          .includes(value.toLowerCase());
      }
      return false;
    },
  });

  const columns: TableProps<DataType>["columns"] = [
    {
      title: t("manualQuotation.columns.freightMethod"),
      dataIndex: "freightmethod",
      key: "freightmethod",
      width: isEnglish ? 150 : 90,
      ...getColumnSearchProps("freightmethod"),
    },
    {
      title: t("manualQuotation.columns.goodsType"),
      dataIndex: "goodstype",
      key: "goodstype",
      width: isEnglish ? 120 : 90,
      ...getColumnSearchProps("goodstype"),
    },
    {
      title: t("manualQuotation.columns.companyName"),
      dataIndex: "companyname",
      key: "companyname",
      width: isEnglish ? 140 : 120,
      ...getColumnSearchProps("companyname"),
    },
    {
      title: t("manualQuotation.columns.inquirer"),
      dataIndex: "inquirer",
      key: "inquirer",
      width: isEnglish ? 100 : 90,
      ...getColumnSearchProps("inquirer"),
    },
    {
      title: t("manualQuotation.columns.originPort"),
      dataIndex: "originport",
      key: "originport",
      width: isEnglish ? 110 : 90,
      ...getColumnSearchProps("originport"),
    },
    {
      title: t("manualQuotation.columns.unloadingPort"),
      dataIndex: "unloadingport",
      key: "unloadingport",
      width: isEnglish ? 140 : 90,
      ...getColumnSearchProps("unloadingport"),
    },
    {
      title: t("manualQuotation.columns.grossWeight"),
      dataIndex: "grossweight",
      key: "grossweight",
      width: isEnglish ? 150 : 110,
      sorter: (a, b) => {
        const aValue = Number(a.grossweight) || 0;
        const bValue = Number(b.grossweight) || 0;
        return aValue - bValue;
      },
    },
    {
      title: t("manualQuotation.columns.goodsVolume"),
      dataIndex: "goodsvolume",
      key: "goodsvolume",
      width: isEnglish ? 160 : 110,
      sorter: (a, b) => {
        const aValue = Number(a.goodsvolume) || 0;
        const bValue = Number(b.goodsvolume) || 0;
        return aValue - bValue;
      },
    },
    {
      title: t("manualQuotation.columns.singleMaxWeight"),
      dataIndex: "singlemaxweight",
      key: "singlemaxweight",
      width: isEnglish ? 180 : 130,
      sorter: (a, b) => {
        const aValue = Number(a.singlemaxweight) || 0;
        const bValue = Number(b.singlemaxweight) || 0;
        return aValue - bValue;
      },
      render: (text) => {
        return text ? `${text}kg` : "-";
      },
    },
    {
      title: t("manualQuotation.columns.specialCargo"),
      dataIndex: "specialcargo",
      key: "specialcargo",
      width: isEnglish ? 120 : 100,
      render: (value) => {
        if (!value) return <Tag>{t("manualQuotation.specialCargo.none")}</Tag>;
        return (
          <Tooltip title={value}>
            <Tag color="orange">{value}</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: t("manualQuotation.columns.status"),
      dataIndex: "status",
      key: "status",
      width: isEnglish ? 100 : 80,
      filters: [
        { text: t("manualQuotation.status.quoted"), value: "quoted" },
        { text: t("manualQuotation.status.unquoted"), value: "unquoted" },
        { text: t("manualQuotation.status.deal"), value: "deal" },
        { text: t("manualQuotation.status.nodeal"), value: "nodeal" },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => {
        let color = "";
        let text = "";
        let icon = null;

        switch (status) {
          case "quoted":
            color = "success";
            text = t("manualQuotation.status.quoted");
            icon = <CheckCircleOutlined />;
            break;
          case "unquoted":
            color = "warning";
            text = t("manualQuotation.status.unquoted");
            icon = <ClockCircleOutlined />;
            break;
          case "deal":
            color = "processing";
            text = t("manualQuotation.status.deal");
            icon = <DollarOutlined />;
            break;
          case "nodeal":
            color = "error";
            text = t("manualQuotation.status.nodeal");
            icon = <CloseCircleOutlined />;
            break;
          default:
            color = "default";
            text = t("manualQuotation.status.unknown");
        }

        return (
          <Tag color={color} icon={icon} className="status-tag">
            {text}
          </Tag>
        );
      },
    },
    {
      title: t("manualQuotation.columns.actions"),
      key: "action",
      width: isEnglish ? 200 : 180,
      render: (_, record) => (
        <Space size={10} className="operation-buttons">
          <Button
            type="primary"
            onClick={() => handleManualQuote(record)}
            size="small"
          >
            {t("common.manualquotation")}
          </Button>
        </Space>
      ),
    },
  ];
  const handleManualQuote = (record: DataType) => {
    setSelectedRecord(record);
    setQuotationEditorVisible(true);
  };

  const handleTableChange: TableProps<DataType>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    setPagination(pagination);
  };

  return (
    <div className="manual-quotation-container">
      <div className="filter_box">
        <div className="title">{t("manualQuotation.title")}</div>
        <Space></Space>
      </div>

      <div className="table-container">
        <Table<DataType>
          className="scroll-table"
          columns={columns}
          dataSource={filteredData}
          pagination={{
            ...pagination,
            showTotal: (total) =>
              t("manualQuotation.pagination.total", { total }),
            showQuickJumper: true,
            showSizeChanger: true,
            size: "small",
          }}
          loading={loading}
          onChange={handleTableChange}
          size="middle"
          rowKey="inquiryid"
          scroll={{
            x: isEnglish ? 1400 : 1200,
            y: "70vh",
          }}
        />
      </div>

      <QuotationEditor
        visible={quotationEditorVisible}
        onClose={() => setQuotationEditorVisible(false)}
        record={selectedRecord}
        onSaveSuccess={fetchManualQuotationList}
      />
    </div>
  );
};

export default ManualQuotation;
