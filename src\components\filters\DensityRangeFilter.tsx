import React, { useState } from "react";
import { InputNumber, Button, Space } from "antd";

interface DensityRangeFilterProps {
  setSelectedKeys: (selectedKeys: string[]) => void;
  confirm: () => void;
  clearFilters: () => void;
  minParamName?: string;
  maxParamName?: string;
}

/**
 * 密度范围筛选组件
 * @param setSelectedKeys 设置选中的键
 * @param confirm 确认筛选
 * @param clearFilters 清除筛选
 * @param minParamName 最小值参数名，默认为 "leftdensity"
 * @param maxParamName 最大值参数名，默认为 "rightdensity"
 */
const DensityRangeFilter: React.FC<DensityRangeFilterProps> = ({
  setSelectedKeys,
  confirm,
  clearFilters,
  minParamName = "leftdensity",
  maxParamName = "rightdensity",
}) => {
  const [minValue, setMinValue] = useState<number | null>(null);
  const [maxValue, setMaxValue] = useState<number | null>(null);

  const handleMinChange = (value: number | null) => {
    setMinValue(value);
    updateSelectedKeys(value, maxValue);
  };

  const handleMaxChange = (value: number | null) => {
    setMaxValue(value);
    updateSelectedKeys(minValue, value);
  };

  const updateSelectedKeys = (min: number | null, max: number | null) => {
    const keys: string[] = [];

    if (min !== null) {
      keys.push(`${minParamName}:${min}`);
    }

    if (max !== null) {
      keys.push(`${maxParamName}:${max}`);
    }

    setSelectedKeys(keys);
  };

  const handleConfirm = () => {
    confirm();
  };

  const handleClear = () => {
    setMinValue(null);
    setMaxValue(null);
    setSelectedKeys([]);
    clearFilters();
    confirm();
  };

  return (
    <div style={{ padding: 8 }}>
      <Space direction="vertical" style={{ width: "100%" }}>
        <div style={{ display: "flex", alignItems: "center" }}>
          <InputNumber
            placeholder="最小值"
            value={minValue}
            onChange={handleMinChange}
            style={{ width: 100 }}
          />
          <span style={{ margin: "0 8px" }}>-</span>
          <InputNumber
            placeholder="最大值"
            value={maxValue}
            onChange={handleMaxChange}
            style={{ width: 100 }}
          />
        </div>
        <Space>
          <Button type="primary" onClick={handleConfirm} size="small">
            确定
          </Button>
          <Button onClick={handleClear} size="small">
            重置
          </Button>
        </Space>
      </Space>
    </div>
  );
};

export default DensityRangeFilter;
