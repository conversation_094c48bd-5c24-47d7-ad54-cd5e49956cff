import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  Tabs,
  Button,
  Table,
  Space,
  Form,
  Input,
  Popconfirm,
  message,
  Tree,
  Row,
  Col,
  Badge,
  Typography,
  Divider,
  Tag,
  Switch,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  TeamOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  PartitionOutlined,
  MailOutlined,
  PhoneOutlined,
  KeyOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { Department, User, userIdentityMap } from "../types";
import type { ColumnsType } from "antd/es/table";
import UserFormModal from "./UserFormModal";
import PasswordFormModal from "./PasswordFormModal";
import TableCom, { TableComRef } from "@/components/TableCom";
import {
  getUserByCondition,
  addUser,
  updateUser,
  delUser,
  updatePassword,
  getAllDepartment,
} from "../services";
import { SHA256 } from "crypto-js";

interface UserItem {
  userid: number;
  email: string;
  departmentid: number;
  fullname: string;
  contact: string;
  createtime: string;
  recentlogintime: string;
  isvalid: boolean;
  useridentity: number;
  userremark: string;
}

const { Text } = Typography;

const UserManagement: React.FC = () => {
  const { t } = useTranslation();
  const [userForm] = Form.useForm();
  const [selectedDepartment, setSelectedDepartment] = useState<number | null>(
    null
  );
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const tableRef = useRef<TableComRef>(null);

  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<{
    userId: number;
    email: string;
  } | null>(null);

  useEffect(() => {
    getAllDepartment().then((res) => {
      setDepartments(res.data.data);
    });
  }, []);

  const handleAddUser = () => {
    setEditingUser(null);
    userForm.resetFields();
    if (selectedDepartment) {
      userForm.setFieldsValue({ departmentid: selectedDepartment });
    }
    setUserModalVisible(true);
  };

  const userColumns: ColumnsType<UserItem> = [
    {
      title: t("organizationManagement.userManagement.columns.email"),
      dataIndex: "email",
      key: "email",
      width: 180,
      render: (_, record: UserItem) => {
        const contactInfo = record.contact || "";
        const parts = contactInfo.split(",");
        const email = parts[0] || "-";

        return (
          <Space>
            <Text strong>{email}</Text>
          </Space>
        );
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.fullname"),
      dataIndex: "fullname",
      key: "fullname",
      width: 120,
      render: (text: string) => {
        return text ? text : "-";
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.department"),
      dataIndex: "departmentid",
      key: "departmentid",
      width: 120,
      render: (departmentid: number) => {
        const department = departments.find(
          (dept) => dept.departmentid === departmentid
        );
        return department ? department.departname : "-";
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.identity"),
      dataIndex: "useridentity",
      key: "useridentity",
      width: 120,
      render: (identity: number) => {
        const identityInfo =
          userIdentityMap[identity as keyof typeof userIdentityMap];
        const identityText = t(
          `organizationManagement.userManagement.userIdentity.${identity}`
        );
        return identityInfo ? (
          <Tag color={identityInfo.color}>{identityText}</Tag>
        ) : (
          "-"
        );
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.phone"),
      key: "contact",
      width: 180,
      render: (_, record: UserItem) => {
        const contactInfo = record.contact || "";
        const parts = contactInfo.split(",");
        const telephone = parts[1] || "-";

        return (
          <Space>
            <PhoneOutlined />
            <Text>{telephone}</Text>
          </Space>
        );
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.status"),
      dataIndex: "isvalid",
      key: "isvalid",
      width: 100,
      render: (isvalid: boolean, record: UserItem) => {
        // 检查用户身份是否为0，如果是则禁用状态切换
        const isSystemUser = record.useridentity === 0;

        return (
          <Tooltip
            title={
              isSystemUser
                ? t(
                    "organizationManagement.userManagement.status.systemUserCannotModify"
                  )
                : ""
            }
          >
            <Switch
              checked={isvalid}
              onChange={(checked) =>
                !isSystemUser && handleToggleUserStatus(record, checked)
              }
              checkedChildren={t(
                "organizationManagement.userManagement.status.enabled"
              )}
              unCheckedChildren={t(
                "organizationManagement.userManagement.status.disabled"
              )}
              disabled={isSystemUser}
            />
          </Tooltip>
        );
      },
    },
    {
      title: t("organizationManagement.userManagement.columns.actions"),
      key: "action",
      width: 120,
      fixed: "right" as const,
      render: (_, record: UserItem) => {
        // 检查用户身份是否为0，如果是则禁用编辑和修改密码功能
        const isSystemUser = record.useridentity === 0;

        return (
          <Space size="middle" className="operation-buttons">
            <Tooltip
              title={
                isSystemUser
                  ? t(
                      "organizationManagement.userManagement.actions.externalUserCannotEdit"
                    )
                  : t("organizationManagement.userManagement.actions.edit")
              }
            >
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => !isSystemUser && handleEditUser(record)}
                className="edit-button"
                size="small"
                disabled={isSystemUser}
              >
                {/* 编辑 */}
              </Button>
            </Tooltip>
            {isSystemUser ? (
              <Tooltip
                title={t(
                  "organizationManagement.userManagement.actions.externalUserCannotDelete"
                )}
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  className="delete-button"
                  size="small"
                  disabled={true}
                >
                  {/* 删除 */}
                </Button>
              </Tooltip>
            ) : (
              <Tooltip
                title={t(
                  "organizationManagement.userManagement.actions.delete"
                )}
              >
                <Popconfirm
                  title={t(
                    "organizationManagement.userManagement.deleteConfirm.title"
                  )}
                  description={t(
                    "organizationManagement.userManagement.deleteConfirm.description"
                  )}
                  onConfirm={() => handleDeleteUser(record.userid)}
                  okText={t(
                    "organizationManagement.userManagement.deleteConfirm.okText"
                  )}
                  cancelText={t(
                    "organizationManagement.userManagement.deleteConfirm.cancelText"
                  )}
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    className="delete-button"
                    size="small"
                  >
                    {/* 删除 */}
                  </Button>
                </Popconfirm>
              </Tooltip>
            )}
            <Tooltip
              title={
                isSystemUser
                  ? t(
                      "organizationManagement.userManagement.actions.externalUserCannotChangePassword"
                    )
                  : t(
                      "organizationManagement.userManagement.actions.changePassword"
                    )
              }
            >
              <Button
                type="text"
                icon={<KeyOutlined />}
                onClick={() => !isSystemUser && handleChangePassword(record)}
                className="password-button"
                size="small"
                disabled={isSystemUser}
              >
                {/* 修改密码 */}
              </Button>
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const handleChangePassword = (record: UserItem) => {
    setCurrentUser({
      userId: record.userid,
      email: record.email,
    });
    setPasswordModalVisible(true);
  };

  const handleToggleUserStatus = async (record: UserItem, status: boolean) => {
    try {
      const response = await updateUser({
        ...record,
        isvalid: status,
      });

      if (response.data?.resultCode === 200) {
        message.success(
          status
            ? t("organizationManagement.userManagement.messages.userEnabled")
            : t("organizationManagement.userManagement.messages.userDisabled")
        );
        tableRef.current?.refresh();
      } else {
        message.error(
          response.data?.message ||
            (status
              ? t("organizationManagement.userManagement.messages.enableFailed")
              : t(
                  "organizationManagement.userManagement.messages.disableFailed"
                ))
        );
      }
    } catch (error) {
      console.error(`${status ? "启用" : "禁用"}用户出错:`, error);
      message.error(
        status
          ? t("organizationManagement.userManagement.messages.enableError")
          : t("organizationManagement.userManagement.messages.disableError")
      );
    }
  };

  const handleDataLoaded = (_data: UserItem[], _totalCount: number) => {
    // console.log(`加载了 ${_totalCount} 条数据`);
  };

  const transformUserData = (data: any[]): UserItem[] => {
    return data.map((user: any) => ({
      ...user,
      contact: `${user.email || ""},${user.telephone || ""}`,
      createtime: user.createtime,
      recentlogintime: user.recentlogintime || "",
      userremark: user.userremark || "",
    }));
  };

  const handleEditUser = (user: any) => {
    const userObj: User = {
      ...user,
      email: user.contact ? user.contact.split(",")[0] : "",
      telephone: user.contact ? user.contact.split(",")[1] : "",
    };

    setEditingUser(userObj);
    userForm.setFieldsValue({
      ...userObj,
      password: "",
    });
    setUserModalVisible(true);
  };

  const handleDeleteUser = async (userId: number) => {
    try {
      const response = await delUser({ userid: userId });
      if (response.data?.resultCode === 200) {
        message.success(
          t("organizationManagement.userManagement.messages.deleteSuccess")
        );
        tableRef.current?.refresh();
      } else {
        message.error(
          response.data?.message ||
            t("organizationManagement.userManagement.messages.deleteFailed")
        );
      }
    } catch (error) {
      console.error("删除用户出错:", error);
      message.error(
        t("organizationManagement.userManagement.messages.deleteError")
      );
    }
  };

  const handleSaveUser = () => {
    userForm
      .validateFields()
      .then(async (values) => {
        try {
          if (editingUser) {
            const userData = {
              ...editingUser,
              ...values,
            };
            const response = await updateUser(userData);
            if (response.data?.resultCode === 200) {
              message.success(
                t(
                  "organizationManagement.userManagement.messages.updateSuccess"
                )
              );
              setUserModalVisible(false);
              tableRef.current?.refresh();
            } else {
              message.error(
                response.data?.message ||
                  t(
                    "organizationManagement.userManagement.messages.updateFailed"
                  )
              );
            }
          } else {
            const userData = {
              ...values,
              isvalid: true,
              password: SHA256(values.password).toString(),
            };
            const response = await addUser(userData);
            if (response.data?.resultCode === 200) {
              message.success(
                t("organizationManagement.userManagement.messages.addSuccess")
              );
              setUserModalVisible(false);
              tableRef.current?.refresh();
            } else {
              message.error(
                response.data?.message ||
                  t("organizationManagement.userManagement.messages.addFailed")
              );
            }
          }
        } catch (error) {
          console.error("保存用户出错:", error);
          message.error(
            t("organizationManagement.userManagement.messages.saveError")
          );
        }
      })
      .catch((info) => {
        console.log("验证失败:", info);
      });
  };

  const filterRender = () => {
    return (
      <div className="filter_box">
        <div className="title">
          {t("organizationManagement.userManagement.title")}
        </div>
        <Divider type="vertical" />
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddUser}
          >
            {t("organizationManagement.userManagement.newUser")}
          </Button>
        </Space>
      </div>
    );
  };

  return (
    <div className="user-container">
      <div className="user-list-container">
        <TableCom<UserItem>
          ref={tableRef}
          columns={userColumns}
          rowKey="userid"
          filterRender={filterRender}
          serverPagination={true}
          api={(params) => getUserByCondition(params) as any}
          extraParams={{ departmentId: selectedDepartment }}
          transformData={transformUserData}
          onDataLoaded={handleDataLoaded}
          size="small"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) =>
              t("organizationManagement.userManagement.pagination.total", {
                total,
              }),
          }}
        />
      </div>

      <UserFormModal
        visible={userModalVisible}
        onCancel={() => setUserModalVisible(false)}
        onOk={handleSaveUser}
        editingUser={editingUser}
        departments={departments}
        form={userForm}
      />

      <PasswordFormModal
        visible={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        userId={currentUser?.userId || null}
        email={currentUser?.email || ""}
      />
    </div>
  );
};

export default UserManagement;
