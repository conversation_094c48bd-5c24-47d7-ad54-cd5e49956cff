import React, { useEffect, useState } from "react";
import "./index.less";
import { FormInstance } from "antd/es/form";
import {
  Button,
  Form,
  Divider,
  Input,
  Alert,
  InputNumber,
  Select,
  Switch,
  DatePicker,
  message,
} from "antd";
import {
  FilterOutlined,
  ReloadOutlined,
  DownOutlined,
  UpOutlined,
} from "@ant-design/icons";
import { useWatch } from "antd/es/form/Form";
import { useTranslation } from "react-i18next";
import { formatDateToTimestamp } from "@/utils/util";

interface SideLeftType {
  form: FormInstance;
  selectQuotation: any;
  setSelectQuotation: React.Dispatch<React.SetStateAction<object>>;
  setSupplyPriceList: (state: any) => Promise<void>;
  portList: Array<any>;
  packageTypeList: Array<any>;
  specialItemsList: Array<any>;
  airlineList: Array<any>;
}

const SideLeft: React.FC<SideLeftType> = (props) => {
  const { t } = useTranslation();
  const {
    form,
    selectQuotation,
    setSelectQuotation,
    setSupplyPriceList,
    portList,
    packageTypeList,
    specialItemsList,
  } = props;
  const [expandedAdvanced, setExpandedAdvanced] = useState(false);
  const isValidity = useWatch("isvalidity", form);

  const onFormLayoutChange = (changedValues: any) => {
    console.log("Form values changed:", changedValues);
  };

  const handleReset = () => {
    form.resetFields();
    setSelectQuotation({});
  };

  const handleApplyFilter = () => {
    form
      .validateFields()
      .then(async (values) => {
        try {
          const formattedValues = {
            ...selectQuotation,
            ...values,
            originport: values?.originport?.join("/"),
            specialcargo: values?.specialcargo?.join(","),
            shipmentdate: formatDateToTimestamp(values?.shipmentdate),
          };
          setSupplyPriceList(formattedValues);
        } catch (e) {
          console.log("Submit error:", e);
          message.error(t("supplyPrice.queryFailed"));
        }
      })
      .catch((e) => {
        console.log("Form validation error:", e);
        message.error(t("supplyPrice.pleaseCompleteRequired"));
      });
  };

  const toggleAdvanced = () => {
    setExpandedAdvanced(!expandedAdvanced);
  };

  return (
    <>
      <div className="filter_container">
        <div className="filter_title">{t("supplyPrice.inquiryInfo")}</div>
        <div className="filter_top">
          {selectQuotation && Object.keys(selectQuotation)?.length > 0 ? (
            <Alert
              message={t("supplyPrice.selectedInquiry")}
              description={
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <span>{t("supplyPrice.inquiryNumber")}:</span>
                  <span style={{ fontWeight: 500, color: "#1890ff" }}>
                    {selectQuotation.inquirycode}
                  </span>
                </div>
              }
              type="info"
              showIcon
            />
          ) : (
            <Alert
              message={t("supplyPrice.noInquirySelected")}
              description={
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <span>{t("supplyPrice.pleaseSelectInquiry")}</span>
                </div>
              }
              type="info"
              showIcon
            />
          )}
        </div>

        <Divider orientation="left">
          <FilterOutlined />
          <span>{t("supplyPrice.filterConditions")}</span>
        </Divider>

        <div className="filter_content">
          <Form
            layout="vertical"
            form={form}
            onValuesChange={onFormLayoutChange}
          >
            {/* 核心筛选条件 */}
            <div className="filter-section">
              <div className="section-title">
                {t("supplyPrice.coreFilters")}
              </div>
              <div className="section-content">
                <Form.Item
                  name="originport"
                  label={t("supplyPrice.fields.originPort")}
                >
                  <Select
                    options={portList}
                    placeholder={t("supplyPrice.placeholders.selectOriginPort")}
                    showSearch
                    optionFilterProp="label"
                    mode="multiple"
                  />
                </Form.Item>
                <Form.Item
                  name="unloadingport"
                  label={t("supplyPrice.fields.destinationPort")}
                  rules={[
                    {
                      required: true,
                      message: t(
                        "supplyPrice.validation.destinationPortRequired"
                      ),
                    },
                  ]}
                >
                  <Select
                    options={portList}
                    placeholder={t(
                      "supplyPrice.placeholders.selectDestinationPort"
                    )}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>

                <Form.Item
                  label={t("supplyPrice.fields.grossWeight")}
                  name="grossweight"
                  rules={[
                    {
                      required: true,
                      message: t("supplyPrice.validation.grossWeightRequired"),
                    },
                  ]}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    step="0.001"
                    placeholder={t("supplyPrice.placeholders.inputGrossWeight")}
                    addonAfter="kgs"
                  />
                </Form.Item>
                <Form.Item
                  label={t("supplyPrice.fields.goodsVolume")}
                  name="goodsvolume"
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    step="0.001"
                    placeholder={t("supplyPrice.placeholders.inputGoodsVolume")}
                    addonAfter="cbm"
                  />
                </Form.Item>
              </div>
            </div>

            {/* 基础筛选条件 */}
            <div className="filter-section">
              <div className="section-title">
                {t("supplyPrice.basicFilters")}
              </div>
              <div className="section-content">
                <Form.Item
                  label={t("supplyPrice.fields.freightMethod")}
                  name="freightmethod"
                >
                  <Select
                    showSearch
                    placeholder={t(
                      "supplyPrice.placeholders.selectFreightMethod"
                    )}
                    optionFilterProp="children"
                    allowClear
                    style={{ width: "100%" }}
                    options={[{ label: "AF", value: "AF" }]}
                  ></Select>
                </Form.Item>

                <Form.Item
                  label={t("supplyPrice.fields.goodsType")}
                  name="goodstype"
                >
                  <Select
                    showSearch
                    placeholder={t("supplyPrice.placeholders.selectGoodsType")}
                    optionFilterProp="children"
                    allowClear
                    style={{ width: "100%" }}
                    options={[
                      { label: t("supplyPrice.goodsTypes.fcl"), value: "FCL" },
                      { label: t("supplyPrice.goodsTypes.lcl"), value: "LCL" },
                    ]}
                  ></Select>
                </Form.Item>

                <Form.Item
                  label={t("supplyPrice.fields.shippedPlace")}
                  name="shippedplace"
                >
                  <Input
                    placeholder={t(
                      "supplyPrice.placeholders.inputShippedPlace"
                    )}
                    style={{ width: "100%" }}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 高级筛选条件*/}
            <div className="advanced-filter-toggle" onClick={toggleAdvanced}>
              {expandedAdvanced ? <UpOutlined /> : <DownOutlined />}
              <span>
                {expandedAdvanced
                  ? t("supplyPrice.collapseAdvanced")
                  : t("supplyPrice.expandAdvanced")}
              </span>
            </div>

            {expandedAdvanced && (
              <div className="advanced-filters">
                <Divider dashed />

                <div className="filter-section">
                  <div className="section-title">
                    {t("supplyPrice.sectionTitles.cargoSize")}
                  </div>
                  <div className="section-content">
                    <Form.Item
                      label={`${t("supplyPrice.fields.cargoLength")}(cm)`}
                      name="cargolength"
                    >
                      <InputNumber
                        style={{ width: "100%" }}
                        step="0.01"
                        placeholder={t(
                          "supplyPrice.placeholders.inputCargoLength"
                        )}
                      />
                    </Form.Item>

                    <Form.Item
                      label={`${t("supplyPrice.fields.cargoWidth")}(cm)`}
                      name="cargowidth"
                    >
                      <InputNumber
                        style={{ width: "100%" }}
                        step="0.01"
                        placeholder={t(
                          "supplyPrice.placeholders.inputCargoWidth"
                        )}
                      />
                    </Form.Item>

                    <Form.Item
                      label={`${t("supplyPrice.fields.cargoHeight")}(cm)`}
                      name="cargoheight"
                    >
                      <InputNumber
                        style={{ width: "100%" }}
                        step="0.01"
                        placeholder={t(
                          "supplyPrice.placeholders.inputCargoHeight"
                        )}
                      />
                    </Form.Item>
                  </div>
                </div>

                <div className="filter-section">
                  <div className="section-title">
                    {t("supplyPrice.sectionTitles.specialOptions")}
                  </div>
                  <div className="section-content">
                    <Form.Item
                      label={t("supplyPrice.fields.isBrand")}
                      name="isbrand"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      label={t("supplyPrice.fields.ensureCabin")}
                      name="ensurecabin"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      label={t("supplyPrice.fields.requireETD")}
                      name="isvalidity"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    {isValidity && (
                      <Form.Item
                        name="shipmentdate"
                        label={t("supplyPrice.fields.shipmentDate")}
                        rules={[
                          {
                            required: true,
                            message: t(
                              "supplyPrice.validation.shipmentDateRequired"
                            ),
                          },
                        ]}
                      >
                        <DatePicker
                          className="full-width"
                          format="YYYY-MM-DD"
                          placeholder={t(
                            "supplyPrice.placeholders.selectShipmentDate"
                          )}
                        />
                      </Form.Item>
                    )}

                    <Form.Item
                      label={t("supplyPrice.fields.packageType")}
                      name="packagetype"
                    >
                      <Select
                        options={packageTypeList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectPackageType"
                        )}
                        showSearch
                        optionFilterProp="label"
                        allowClear
                      />
                    </Form.Item>

                    <Form.Item
                      label={t("supplyPrice.fields.specialCargo")}
                      name="specialcargo"
                    >
                      <Select
                        mode="multiple"
                        options={specialItemsList}
                        placeholder={t(
                          "supplyPrice.placeholders.selectSpecialCargo"
                        )}
                        optionFilterProp="label"
                        maxTagCount={3}
                        maxTagTextLength={4}
                        maxTagPlaceholder={(omittedValues) =>
                          `+${omittedValues.length}...`
                        }
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
            )}
          </Form>
        </div>
      </div>

      <div className="filter_footer">
        <Button
          type="primary"
          icon={<FilterOutlined />}
          onClick={handleApplyFilter}
        >
          {t("supplyPrice.applyFilter")}
        </Button>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>
          {t("supplyPrice.reset")}
        </Button>
      </div>
    </>
  );
};

export default SideLeft;
