import React from "react";
import { Navigate } from "react-router-dom";
import { useAppSelector } from "@/store/hooks";

/**
 * 默认路由组件
 * 根据当前登录用户的角色动态跳转到对应的默认页面
 */
const DefaultRoute: React.FC = () => {
  const { user } = useAppSelector((state) => state.user);

  // 根据用户身份决定默认路由
  const getDefaultRoute = () => {
    if (!user) {
      return "/login";
    }

    switch (user.useridentity) {
      case 0: // 普通用户
        return "/ai_quotation";
      case 1: // 询价业务员
      case 3: // 询价部门主管
        return "/quotation";
      case 2: // 价格业务员
      case 4: // 价格部门主管
        return "/international_price";
      case 5: // 系统管理员
        return "/airline_management";
      default:
        return "/quotation";
    }
  };

  const defaultRoute = getDefaultRoute();

  return <Navigate to={defaultRoute} replace />;
};

export default DefaultRoute;
