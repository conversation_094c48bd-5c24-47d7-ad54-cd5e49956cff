import React from "react";
import { Typo<PERSON>, Badge, Tag, Button } from "antd";

const { Text } = Typography;

interface QuotationCardProps {
  item: any;
  handleQuotationSelect: (quotation: any) => void;
  showActions?: boolean;
}

const QuotationCard: React.FC<QuotationCardProps> = ({
  item,
  handleQuotationSelect,
  showActions = false,
}) => {
  const getAirlineName = () => {
    return item.airlinename || item.airlines || item.airline || "未知航司";
  };

  const getOriginPort = () => {
    return item.originport || item.originPort || "";
  };

  const getDestinationPort = () => {
    return item.unloadingport || item.destinationPort || "";
  };

  const getPrice = () => {
    if (item.totalprice) {
      return (item.totalprice / 7.2).toFixed(2);
    }
    return item.price || 0;
  };

  const getCurrency = () => {
    return item.currency || "USD";
  };

  const getTransitTime = () => {
    if (item.validity) {
      return `${item.validity} ${item.validity > 1 ? "days" : "day"}`;
    }
    return item.transitTime || "未知";
  };

  const getSchedule = () => {
    if (item.originschedules) {
      return [item.originschedules];
    }
    return item.departureSchedule || [];
  };

  return (
    <div className="quotation-list-card">
      <div className="card-content">
        <div className="quotation-header">
          <div className="quotation-airline">
            <Text strong className="airline-name">
              {getAirlineName()}
            </Text>
            {item.recommended && (
              <Badge
                status="processing"
                text="推荐"
                className="recommendation-badge"
              />
            )}
          </div>
          <div className="quotation-price">
            <Text className="price-value">{getPrice()}</Text>
            <Text className="price-currency">{getCurrency()}</Text>
          </div>
        </div>

        <div className="quotation-route">
          <Text>
            {getOriginPort()} → {getDestinationPort()}
          </Text>
          <Text>运输时间: {getTransitTime()}</Text>
        </div>

        <div className="quotation-details">
          <div className="schedule-tags">
            {getSchedule().map((day: any, index: number) => (
              <Tag key={index}>{day}</Tag>
            ))}
          </div>
        </div>

        {showActions && (
          <div className="card-actions">
            <Button type="default" onClick={() => handleQuotationSelect(item)}>
              查看详情
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuotationCard;
