const CracoLessPlugin = require("craco-less");
const path = require("path");
const webpack = require("webpack");

module.exports = {
  plugins: [
    {
      plugin: CracoLessPlugin,
      options: {
        lessLoaderOptions: {
          lessOptions: {
            modifyVars: {
              hack: `true; @import "${path.resolve(__dirname, "src/assets/styles/variables.less")}";`,
            },
            javascriptEnabled: true,
          },
        },
      },
    },
  ],
  babel: {
    plugins: [
      [
        "import",
        {
          libraryName: "antd",
          libraryDirectory: "es",
          style: true,
        },
        "antd",
      ],
    ],
  },
  webpack: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 设置 `@` 指向 `src` 目录
      "@components": path.resolve(__dirname, "src/components"),
      "@pages": path.resolve(__dirname, "src/pages"),
      "@utils": path.resolve(__dirname, "src/utils"),
      "@assets": path.resolve(__dirname, "src/assets"),
    },
    configure: (webpackConfig) => {
      // 添加进度条插件
      webpackConfig.plugins.push(
        new webpack.ProgressPlugin((percentage, message, ...args) => {
          if (process.stdout.isTTY) {
            const percent = Math.round(percentage * 100);
            const progressBar =
              "█".repeat(Math.round(percent / 5)) +
              "░".repeat(20 - Math.round(percent / 5));
            const info = args.length > 0 ? ` ${args[0]}` : "";

            process.stdout.write(
              `\r\x1b[K🚀 [${progressBar}] ${percent}% ${message}${info}`
            );

            if (percentage === 1) {
              console.log("\n✅ 构建完成!");
            }
          } else {
            if (percentage === 0) console.log("🚀 开始构建...");
            if (percentage === 1) console.log("✅ 构建完成!");
          }
        })
      );

      // 忽略第三方库的 source map 警告
      webpackConfig.ignoreWarnings = [
        {
          module:
            /node_modules\/@react-three\/drei\/node_modules\/@mediapipe\/tasks-vision/,
          message: /Failed to parse source map/,
        },
        // 忽略所有 source map 相关的警告
        /Failed to parse source map/,
      ];

      // 生产环境优化
      if (process.env.NODE_ENV === "production") {
        // 禁用 source maps 以减少体积
        webpackConfig.devtool = false;

        // 代码分割优化
        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          splitChunks: {
            chunks: "all",
            maxSize: 244000, // 限制单个chunk大小为244KB
            cacheGroups: {
              // 将antd单独打包
              antd: {
                name: "antd",
                test: /[\\/]node_modules[\\/]antd[\\/]/,
                priority: 20,
                chunks: "all",
                enforce: true,
              },
              // 将three.js相关库单独打包
              three: {
                name: "three",
                test: /[\\/]node_modules[\\/](@react-three|three)[\\/]/,
                priority: 15,
                chunks: "all",
                enforce: true,
              },
              // 将echarts单独打包
              echarts: {
                name: "echarts",
                test: /[\\/]node_modules[\\/]echarts[\\/]/,
                priority: 15,
                chunks: "all",
                enforce: true,
              },
              // 将d3单独打包
              d3: {
                name: "d3",
                test: /[\\/]node_modules[\\/]d3[\\/]/,
                priority: 15,
                chunks: "all",
                enforce: true,
              },
              // React相关库
              react: {
                name: "react",
                test: /[\\/]node_modules[\\/](react|react-dom|react-router)[\\/]/,
                priority: 18,
                chunks: "all",
                enforce: true,
              },
              // 其他vendor库
              vendor: {
                name: "vendor",
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: "all",
                minChunks: 1,
                maxSize: 500000, // 限制vendor chunk大小
              },
            },
          },
        };
      }
      return webpackConfig;
    },
  },
};
